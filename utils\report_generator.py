# -*- coding: utf-8 -*-
"""
مولد التقارير - Report Generator
ينشئ تقارير PDF وExcel ورسوم بيانية
"""

import os
from datetime import date, datetime, timedelta
from typing import List, Dict, Optional
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import seaborn as sns
import pandas as pd
import numpy as np
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.lib import colors
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import arabic_reshaper
from bidi.algorithm import get_display

# إعداد matplotlib للعربية
plt.rcParams['font.family'] = ['Arial Unicode MS', 'Tahoma', 'Deja<PERSON><PERSON>']
plt.rcParams['axes.unicode_minus'] = False

class ReportGenerator:
    """مولد التقارير المتقدم"""
    
    def __init__(self, output_dir: str = "reports"):
        self.output_dir = output_dir
        self.ensure_output_dir()
        
        # إعداد الألوان
        self.colors = {
            'primary': '#2E86AB',
            'secondary': '#A23B72',
            'success': '#F18F01',
            'warning': '#C73E1D',
            'info': '#6C757D'
        }
    
    def ensure_output_dir(self):
        """التأكد من وجود مجلد التقارير"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def format_arabic_text(self, text: str) -> str:
        """تنسيق النص العربي للعرض الصحيح"""
        try:
            reshaped_text = arabic_reshaper.reshape(text)
            return get_display(reshaped_text)
        except:
            return text
    
    # ===== تقارير الحليب =====
    
    def generate_daily_milk_report(self, milk_records: List[Dict], 
                                 target_date: date) -> str:
        """تقرير إنتاج الحليب اليومي"""
        
        if not milk_records:
            return self._generate_empty_report("تقرير الحليب اليومي", target_date)
        
        # إنشاء DataFrame
        df = pd.DataFrame(milk_records)
        
        # حساب الإحصائيات
        total_milk = df['daily_total'].sum()
        avg_milk = df['daily_total'].mean()
        max_milk = df['daily_total'].max()
        min_milk = df['daily_total'].min()
        cow_count = len(df)
        
        # إنشاء الرسم البياني
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # رسم بياني للإنتاج حسب البقرة
        ax1.bar(range(len(df)), df['daily_total'], color=self.colors['primary'])
        ax1.set_title('إنتاج الحليب حسب البقرة', fontsize=14, fontweight='bold')
        ax1.set_xlabel('رقم البقرة')
        ax1.set_ylabel('الحليب (لتر)')
        ax1.tick_params(axis='x', rotation=45)
        
        # رسم بياني دائري للتوزيع
        top_producers = df.nlargest(5, 'daily_total')
        others_total = total_milk - top_producers['daily_total'].sum()
        
        labels = [f"بقرة {row['cow_id']}" for _, row in top_producers.iterrows()]
        sizes = top_producers['daily_total'].tolist()
        
        if others_total > 0:
            labels.append('أخرى')
            sizes.append(others_total)
        
        ax2.pie(sizes, labels=labels, autopct='%1.1f%%', startangle=90)
        ax2.set_title('توزيع الإنتاج - أعلى 5 منتجات', fontsize=14, fontweight='bold')
        
        plt.tight_layout()
        chart_path = os.path.join(self.output_dir, f'daily_milk_chart_{target_date}.png')
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        # إنشاء تقرير PDF
        filename = f'daily_milk_report_{target_date}.pdf'
        filepath = os.path.join(self.output_dir, filename)
        
        doc = SimpleDocTemplate(filepath, pagesize=A4)
        story = []
        styles = getSampleStyleSheet()
        
        # العنوان
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=1  # وسط
        )
        
        title = f"تقرير إنتاج الحليب اليومي - {target_date}"
        story.append(Paragraph(self.format_arabic_text(title), title_style))
        story.append(Spacer(1, 20))
        
        # ملخص الإحصائيات
        summary_data = [
            ['المؤشر', 'القيمة'],
            ['عدد الأبقار المنتجة', str(cow_count)],
            ['إجمالي الإنتاج (لتر)', f'{total_milk:.1f}'],
            ['متوسط الإنتاج (لتر)', f'{avg_milk:.1f}'],
            ['أعلى إنتاج (لتر)', f'{max_milk:.1f}'],
            ['أقل إنتاج (لتر)', f'{min_milk:.1f}']
        ]
        
        summary_table = Table(summary_data, colWidths=[3*inch, 2*inch])
        summary_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(summary_table)
        story.append(Spacer(1, 20))
        
        # إضافة الرسم البياني
        if os.path.exists(chart_path):
            story.append(Image(chart_path, width=6*inch, height=3*inch))
            story.append(Spacer(1, 20))
        
        # تفاصيل الإنتاج
        story.append(Paragraph(self.format_arabic_text("تفاصيل الإنتاج"), styles['Heading2']))
        
        detail_data = [['رقم البقرة', 'اسم البقرة', 'صباحي (لتر)', 'مسائي (لتر)', 'الإجمالي (لتر)']]
        
        for record in milk_records:
            detail_data.append([
                record['cow_id'],
                record['cow_name'],
                f"{record['morning_milk']:.1f}",
                f"{record['evening_milk']:.1f}",
                f"{record['daily_total']:.1f}"
            ])
        
        detail_table = Table(detail_data, colWidths=[1*inch, 2*inch, 1*inch, 1*inch, 1*inch])
        detail_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(detail_table)
        
        doc.build(story)
        return filepath
    
    def generate_weekly_milk_trend(self, cow_id: str, milk_records: List[Dict]) -> str:
        """رسم بياني لاتجاه الحليب الأسبوعي"""
        
        if not milk_records:
            return None
        
        # تحويل البيانات
        df = pd.DataFrame(milk_records)
        df['date'] = pd.to_datetime(df['date'])
        df = df.sort_values('date')
        
        # إنشاء الرسم البياني
        fig, ax = plt.subplots(figsize=(12, 6))
        
        # رسم الخط
        ax.plot(df['date'], df['daily_total'], marker='o', linewidth=2, 
                markersize=6, color=self.colors['primary'])
        
        # إضافة خط الاتجاه
        z = np.polyfit(range(len(df)), df['daily_total'], 1)
        p = np.poly1d(z)
        ax.plot(df['date'], p(range(len(df))), "--", alpha=0.7, color=self.colors['warning'])
        
        # تنسيق المحاور
        ax.set_title(f'اتجاه إنتاج الحليب - البقرة {cow_id}', fontsize=16, fontweight='bold')
        ax.set_xlabel('التاريخ', fontsize=12)
        ax.set_ylabel('إنتاج الحليب (لتر)', fontsize=12)
        
        # تنسيق التواريخ
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax.xaxis.set_major_locator(mdates.DayLocator(interval=1))
        plt.xticks(rotation=45)
        
        # إضافة شبكة
        ax.grid(True, alpha=0.3)
        
        # إضافة إحصائيات
        avg_milk = df['daily_total'].mean()
        ax.axhline(y=avg_milk, color=self.colors['success'], linestyle=':', 
                  label=f'المتوسط: {avg_milk:.1f} لتر')
        ax.legend()
        
        plt.tight_layout()
        
        chart_path = os.path.join(self.output_dir, f'weekly_trend_{cow_id}.png')
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return chart_path
    
    # ===== تقارير التغذية =====
    
    def generate_feed_analysis_report(self, feed_mix_data: Dict, 
                                    nutritional_analysis: Dict) -> str:
        """تقرير تحليل خلطة العلف"""
        
        filename = f'feed_analysis_{feed_mix_data["mix_id"]}.pdf'
        filepath = os.path.join(self.output_dir, filename)
        
        doc = SimpleDocTemplate(filepath, pagesize=A4)
        story = []
        styles = getSampleStyleSheet()
        
        # العنوان
        title = f"تقرير تحليل خلطة العلف - {feed_mix_data['name']}"
        story.append(Paragraph(self.format_arabic_text(title), styles['Title']))
        story.append(Spacer(1, 20))
        
        # معلومات أساسية
        basic_info = [
            ['رقم الخلطة', feed_mix_data['mix_id']],
            ['اسم الخلطة', feed_mix_data['name']],
            ['تاريخ الإنشاء', feed_mix_data['creation_date']],
            ['التكلفة (ريال/كغ)', f"{feed_mix_data['cost_per_kg']:.3f}"],
            ['التكلفة (ريال/طن)', f"{feed_mix_data['cost_per_ton']:.2f}"],
            ['نقاط الجودة', f"{feed_mix_data['quality_score']:.1f}/100"]
        ]
        
        info_table = Table(basic_info, colWidths=[2*inch, 3*inch])
        info_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(info_table)
        story.append(Spacer(1, 20))
        
        # التحليل الغذائي
        story.append(Paragraph(self.format_arabic_text("التحليل الغذائي"), styles['Heading2']))
        
        nutrition_data = [['العنصر الغذائي', 'القيمة', 'الوحدة']]
        
        nutrition_items = [
            ('المادة الجافة', 'dry_matter', '%'),
            ('البروتين الخام', 'crude_protein', '%'),
            ('الطاقة', 'energy_mcal', 'ميجا كالوري/كغ'),
            ('الألياف الخام', 'crude_fiber', '%'),
            ('الكالسيوم', 'calcium', '%'),
            ('الفوسفور', 'phosphorus', '%')
        ]
        
        for name, key, unit in nutrition_items:
            value = nutritional_analysis.get(key, 0)
            nutrition_data.append([name, f"{value:.2f}", unit])
        
        nutrition_table = Table(nutrition_data, colWidths=[2*inch, 1.5*inch, 1.5*inch])
        nutrition_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(nutrition_table)
        story.append(Spacer(1, 20))
        
        # التحذيرات والتوصيات
        if feed_mix_data.get('warnings'):
            story.append(Paragraph(self.format_arabic_text("تحذيرات"), styles['Heading3']))
            for warning in feed_mix_data['warnings']:
                story.append(Paragraph(f"• {self.format_arabic_text(warning)}", styles['Normal']))
            story.append(Spacer(1, 10))
        
        if feed_mix_data.get('recommendations'):
            story.append(Paragraph(self.format_arabic_text("توصيات"), styles['Heading3']))
            for recommendation in feed_mix_data['recommendations']:
                story.append(Paragraph(f"• {self.format_arabic_text(recommendation)}", styles['Normal']))
        
        doc.build(story)
        return filepath
    
    # ===== تقارير القطيع =====
    
    def generate_herd_summary_report(self, herd_stats: Dict, cows_data: List[Dict]) -> str:
        """تقرير ملخص القطيع"""
        
        filename = f'herd_summary_{date.today()}.pdf'
        filepath = os.path.join(self.output_dir, filename)
        
        doc = SimpleDocTemplate(filepath, pagesize=A4)
        story = []
        styles = getSampleStyleSheet()
        
        # العنوان
        title = f"تقرير ملخص القطيع - {date.today()}"
        story.append(Paragraph(self.format_arabic_text(title), styles['Title']))
        story.append(Spacer(1, 20))
        
        # الإحصائيات العامة
        general_data = [
            ['المؤشر', 'القيمة'],
            ['إجمالي الأبقار', str(herd_stats['total_cows'])],
            ['الأبقار النشطة', str(herd_stats['active_cows'])],
            ['متوسط الوزن (كغ)', f"{herd_stats['average_weight']:.1f}"],
            ['متوسط الإنتاج (لتر/يوم)', f"{herd_stats['herd_average_milk']:.1f}"]
        ]
        
        general_table = Table(general_data, colWidths=[3*inch, 2*inch])
        general_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(general_table)
        story.append(Spacer(1, 20))
        
        # توزيع السلالات
        if herd_stats.get('breed_distribution'):
            story.append(Paragraph(self.format_arabic_text("توزيع السلالات"), styles['Heading2']))
            
            breed_data = [['السلالة', 'العدد', 'متوسط الإنتاج']]
            for breed_info in herd_stats['breed_distribution']:
                breed_data.append([
                    breed_info['breed'],
                    str(breed_info['count']),
                    f"{breed_info.get('avg_milk', 0):.1f}"
                ])
            
            breed_table = Table(breed_data, colWidths=[2*inch, 1*inch, 2*inch])
            breed_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(breed_table)
        
        doc.build(story)
        return filepath
    
    def _generate_empty_report(self, title: str, target_date: date) -> str:
        """إنشاء تقرير فارغ"""
        filename = f'empty_report_{target_date}.pdf'
        filepath = os.path.join(self.output_dir, filename)
        
        doc = SimpleDocTemplate(filepath, pagesize=A4)
        story = []
        styles = getSampleStyleSheet()
        
        story.append(Paragraph(self.format_arabic_text(title), styles['Title']))
        story.append(Spacer(1, 20))
        story.append(Paragraph(self.format_arabic_text("لا توجد بيانات متاحة لهذا التاريخ"), styles['Normal']))
        
        doc.build(story)
        return filepath
