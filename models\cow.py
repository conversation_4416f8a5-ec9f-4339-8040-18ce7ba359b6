# -*- coding: utf-8 -*-
"""
نموذج البقرة - Cow Model
يحتوي على جميع المعلومات المتعلقة بالبقرة الواحدة
"""

from datetime import datetime, date
from typing import Optional, List, Dict
import json

class Cow:
    """فئة البقرة الحلوب"""
    
    def __init__(self, cow_id: str, name: str = "", breed: str = "holstein", 
                 birth_date: Optional[date] = None, weight: float = 600.0):
        self.cow_id = cow_id
        self.name = name or f"بقرة رقم {cow_id}"
        self.breed = breed
        self.birth_date = birth_date or date.today()
        self.weight = weight
        self.registration_date = date.today()
        self.last_calving_date: Optional[date] = None
        self.health_status = "سليمة"  # سليمة، مريضة، حامل، جافة
        self.lactation_number = 1
        self.is_active = True
        self.notes = ""
        
        # بيانات الإنتاج
        self.daily_milk_records: List[Dict] = []
        self.average_daily_milk = 0.0
        self.total_lactation_milk = 0.0
        
        # بيانات التغذية
        self.daily_feed_intake = 22.0  # كغ مادة جافة
        self.body_condition_score = 3.0  # من 1 إلى 5
        
    @property
    def age_in_months(self) -> int:
        """حساب عمر البقرة بالشهور"""
        today = date.today()
        return (today.year - self.birth_date.year) * 12 + (today.month - self.birth_date.month)
    
    @property
    def age_in_years(self) -> float:
        """حساب عمر البقرة بالسنوات"""
        return self.age_in_months / 12.0
    
    @property
    def days_in_milk(self) -> int:
        """عدد الأيام منذ آخر ولادة"""
        if not self.last_calving_date:
            return 0
        return (date.today() - self.last_calving_date).days
    
    @property
    def lactation_stage(self) -> str:
        """مرحلة الحليب الحالية"""
        dim = self.days_in_milk
        if dim == 0:
            return "جافة"
        elif dim <= 100:
            return "مبكرة"
        elif dim <= 200:
            return "متوسطة"
        elif dim <= 305:
            return "متأخرة"
        else:
            return "نهاية الحليب"
    
    def add_milk_record(self, date_recorded: date, morning_milk: float, 
                       evening_milk: float, notes: str = ""):
        """إضافة سجل إنتاج حليب يومي"""
        daily_total = morning_milk + evening_milk
        record = {
            'date': date_recorded.isoformat(),
            'morning_milk': morning_milk,
            'evening_milk': evening_milk,
            'daily_total': daily_total,
            'notes': notes,
            'days_in_milk': self.days_in_milk
        }
        self.daily_milk_records.append(record)
        self._update_milk_statistics()
    
    def _update_milk_statistics(self):
        """تحديث إحصائيات الحليب"""
        if not self.daily_milk_records:
            return
        
        # حساب المتوسط اليومي
        total_milk = sum(record['daily_total'] for record in self.daily_milk_records)
        self.average_daily_milk = total_milk / len(self.daily_milk_records)
        
        # حساب إجمالي الحليب في الموسم الحالي
        current_lactation_records = [
            record for record in self.daily_milk_records 
            if record['days_in_milk'] > 0
        ]
        self.total_lactation_milk = sum(
            record['daily_total'] for record in current_lactation_records
        )
    
    def get_milk_records_by_date_range(self, start_date: date, end_date: date) -> List[Dict]:
        """الحصول على سجلات الحليب في فترة محددة"""
        return [
            record for record in self.daily_milk_records
            if start_date <= date.fromisoformat(record['date']) <= end_date
        ]
    
    def get_recent_milk_trend(self, days: int = 7) -> Dict:
        """تحليل اتجاه إنتاج الحليب في الأيام الأخيرة"""
        if len(self.daily_milk_records) < days:
            return {'trend': 'insufficient_data', 'change_percent': 0}
        
        recent_records = self.daily_milk_records[-days:]
        old_records = self.daily_milk_records[-days*2:-days] if len(self.daily_milk_records) >= days*2 else []
        
        if not old_records:
            return {'trend': 'insufficient_data', 'change_percent': 0}
        
        recent_avg = sum(r['daily_total'] for r in recent_records) / len(recent_records)
        old_avg = sum(r['daily_total'] for r in old_records) / len(old_records)
        
        change_percent = ((recent_avg - old_avg) / old_avg) * 100 if old_avg > 0 else 0
        
        if change_percent > 5:
            trend = 'increasing'
        elif change_percent < -5:
            trend = 'decreasing'
        else:
            trend = 'stable'
        
        return {
            'trend': trend,
            'change_percent': round(change_percent, 2),
            'recent_average': round(recent_avg, 2),
            'previous_average': round(old_avg, 2)
        }
    
    def calculate_feed_requirements(self) -> Dict:
        """حساب احتياجات العلف حسب الوزن وإنتاج الحليب"""
        # احتياجات الصيانة
        maintenance_energy = 0.08 * (self.weight ** 0.75)
        
        # احتياجات الإنتاج (0.74 ميجا كالوري لكل لتر حليب)
        production_energy = self.average_daily_milk * 0.74
        
        # إجمالي الطاقة المطلوبة
        total_energy = maintenance_energy + production_energy
        
        # احتياجات البروتين (حسب الوزن والإنتاج)
        maintenance_protein = self.weight * 0.0006  # كغ
        production_protein = self.average_daily_milk * 0.078  # كغ
        total_protein = maintenance_protein + production_protein
        
        return {
            'total_energy_mcal': round(total_energy, 2),
            'total_protein_kg': round(total_protein, 2),
            'dry_matter_kg': round(self.weight * 0.035, 2),  # 3.5% من وزن الجسم
            'maintenance_energy': round(maintenance_energy, 2),
            'production_energy': round(production_energy, 2),
            'maintenance_protein': round(maintenance_protein, 2),
            'production_protein': round(production_protein, 2)
        }
    
    def to_dict(self) -> Dict:
        """تحويل بيانات البقرة إلى قاموس"""
        return {
            'cow_id': self.cow_id,
            'name': self.name,
            'breed': self.breed,
            'birth_date': self.birth_date.isoformat(),
            'weight': self.weight,
            'registration_date': self.registration_date.isoformat(),
            'last_calving_date': self.last_calving_date.isoformat() if self.last_calving_date else None,
            'health_status': self.health_status,
            'lactation_number': self.lactation_number,
            'is_active': self.is_active,
            'notes': self.notes,
            'daily_feed_intake': self.daily_feed_intake,
            'body_condition_score': self.body_condition_score,
            'daily_milk_records': self.daily_milk_records,
            'average_daily_milk': self.average_daily_milk,
            'total_lactation_milk': self.total_lactation_milk
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'Cow':
        """إنشاء كائن بقرة من قاموس"""
        cow = cls(
            cow_id=data['cow_id'],
            name=data['name'],
            breed=data['breed'],
            birth_date=date.fromisoformat(data['birth_date']),
            weight=data['weight']
        )
        
        cow.registration_date = date.fromisoformat(data['registration_date'])
        if data['last_calving_date']:
            cow.last_calving_date = date.fromisoformat(data['last_calving_date'])
        cow.health_status = data['health_status']
        cow.lactation_number = data['lactation_number']
        cow.is_active = data['is_active']
        cow.notes = data['notes']
        cow.daily_feed_intake = data['daily_feed_intake']
        cow.body_condition_score = data['body_condition_score']
        cow.daily_milk_records = data['daily_milk_records']
        cow.average_daily_milk = data['average_daily_milk']
        cow.total_lactation_milk = data['total_lactation_milk']
        
        return cow

    def to_dict(self):
        """تحويل البقرة إلى قاموس للـ JSON"""
        return {
            'cow_id': self.cow_id,
            'name': self.name,
            'breed': self.breed,
            'birth_date': self.birth_date.isoformat() if self.birth_date else None,
            'weight': self.weight,
            'registration_date': self.registration_date.isoformat() if self.registration_date else None,
            'last_calving_date': self.last_calving_date.isoformat() if self.last_calving_date else None,
            'health_status': self.health_status,
            'lactation_number': self.lactation_number,
            'is_active': self.is_active,
            'notes': self.notes,
            'daily_feed_intake': self.daily_feed_intake,
            'body_condition_score': self.body_condition_score,
            'average_daily_milk': self.average_daily_milk,
            'total_lactation_milk': self.total_lactation_milk,
            'age_in_years': self.age_in_years,
            'days_in_milk': self.days_in_milk,
            'lactation_stage': self.lactation_stage
        }

    def __str__(self):
        return f"البقرة {self.name} ({self.cow_id}) - {self.breed}"
