# -*- coding: utf-8 -*-
"""
محلل التغذية - Nutrition Analyzer
يحلل احتياجات التغذية ويقدم توصيات
"""

from typing import Dict, List, Optional, Tuple
from datetime import date, timedelta
import numpy as np
from models.cow import Cow
from models.feed_mix import FeedMix
from config.nutrition_standards import DAILY_REQUIREMENTS, BREED_WEIGHTS, WARNING_LIMITS

class NutritionAnalyzer:
    """محلل التغذية المتقدم"""
    
    def __init__(self):
        self.standard_requirements = DAILY_REQUIREMENTS['standard_cow']
    
    def calculate_cow_requirements(self, cow: Cow) -> Dict:
        """حساب الاحتياجات الغذائية المخصصة للبقرة"""
        
        # الحصول على معلومات السلالة
        breed_info = BREED_WEIGHTS.get(cow.breed, BREED_WEIGHTS['local'])
        
        # تعديل الاحتياجات حسب الوزن
        weight_factor = cow.weight / 600.0  # الوزن المرجعي 600 كغ
        
        # تعديل الاحتياجات حسب إنتاج الحليب
        milk_factor = cow.average_daily_milk / 25.0  # الإنتاج المرجعي 25 لتر
        
        # تعديل الاحتياجات حسب مرحلة الحليب
        lactation_factor = self._get_lactation_factor(cow.lactation_stage)
        
        # تعديل الاحتياجات حسب حالة الجسم
        body_condition_factor = self._get_body_condition_factor(cow.body_condition_score)
        
        # حساب الاحتياجات المعدلة
        requirements = {}
        
        # المادة الجافة (3.0-4.0% من وزن الجسم)
        requirements['dry_matter'] = cow.weight * 0.035 * weight_factor
        
        # البروتين الخام
        base_protein = self.standard_requirements['crude_protein']
        requirements['crude_protein'] = base_protein * milk_factor * lactation_factor
        
        # الطاقة
        base_energy = self.standard_requirements['energy_mcal']
        requirements['energy_mcal'] = base_energy * weight_factor * milk_factor * body_condition_factor
        
        # الألياف
        requirements['fiber'] = self.standard_requirements['fiber']
        
        # المعادن
        requirements['calcium'] = self.standard_requirements['calcium'] * milk_factor
        requirements['phosphorus'] = self.standard_requirements['phosphorus'] * milk_factor
        
        # الفيتامينات
        requirements['vitamin_a'] = self.standard_requirements['vitamin_a'] * weight_factor
        requirements['vitamin_d'] = self.standard_requirements['vitamin_d'] * weight_factor
        requirements['vitamin_e'] = self.standard_requirements['vitamin_e'] * weight_factor
        
        return {
            'cow_id': cow.cow_id,
            'cow_name': cow.name,
            'weight': cow.weight,
            'average_milk': cow.average_daily_milk,
            'lactation_stage': cow.lactation_stage,
            'body_condition': cow.body_condition_score,
            'requirements': requirements,
            'factors': {
                'weight_factor': round(weight_factor, 2),
                'milk_factor': round(milk_factor, 2),
                'lactation_factor': round(lactation_factor, 2),
                'body_condition_factor': round(body_condition_factor, 2)
            }
        }
    
    def _get_lactation_factor(self, lactation_stage: str) -> float:
        """عامل تعديل حسب مرحلة الحليب"""
        factors = {
            'جافة': 0.7,
            'مبكرة': 1.2,
            'متوسطة': 1.0,
            'متأخرة': 0.9,
            'نهاية الحليب': 0.8
        }
        return factors.get(lactation_stage, 1.0)
    
    def _get_body_condition_factor(self, body_condition_score: float) -> float:
        """عامل تعديل حسب حالة الجسم"""
        if body_condition_score < 2.5:
            return 1.1  # تحتاج طاقة إضافية لزيادة الوزن
        elif body_condition_score > 3.5:
            return 0.9  # تحتاج طاقة أقل لتقليل الوزن
        else:
            return 1.0  # حالة مثلى
    
    def analyze_feed_adequacy(self, cow: Cow, feed_mix: FeedMix) -> Dict:
        """تحليل كفاية العلف للبقرة"""
        
        cow_requirements = self.calculate_cow_requirements(cow)['requirements']
        feed_analysis = feed_mix.nutritional_analysis
        
        # حساب الكمية المطلوبة من الخلطة
        required_dm = cow_requirements['dry_matter']
        feed_dm_content = feed_analysis.get('dry_matter', 90) / 100
        required_feed_kg = required_dm / feed_dm_content
        
        # حساب ما تحصل عليه البقرة من كل عنصر
        actual_intake = {}
        adequacy_ratios = {}
        
        for nutrient in ['crude_protein', 'energy_mcal', 'fiber', 'calcium', 'phosphorus']:
            if nutrient in feed_analysis and nutrient in cow_requirements:
                if nutrient == 'energy_mcal':
                    actual_intake[nutrient] = feed_analysis[nutrient] * required_feed_kg
                else:
                    actual_intake[nutrient] = (feed_analysis[nutrient] / 100) * required_feed_kg * 100
                
                required_amount = cow_requirements[nutrient]
                if nutrient == 'energy_mcal':
                    adequacy_ratios[nutrient] = actual_intake[nutrient] / required_amount
                else:
                    adequacy_ratios[nutrient] = actual_intake[nutrient] / required_amount
        
        # تقييم الكفاية
        deficiencies = []
        excesses = []
        
        for nutrient, ratio in adequacy_ratios.items():
            if ratio < 0.9:
                deficiencies.append({
                    'nutrient': nutrient,
                    'ratio': round(ratio, 2),
                    'deficit_percent': round((1 - ratio) * 100, 1)
                })
            elif ratio > 1.2:
                excesses.append({
                    'nutrient': nutrient,
                    'ratio': round(ratio, 2),
                    'excess_percent': round((ratio - 1) * 100, 1)
                })
        
        # تقييم عام
        overall_score = np.mean(list(adequacy_ratios.values())) * 100
        
        if overall_score >= 95:
            adequacy_status = "ممتاز"
        elif overall_score >= 85:
            adequacy_status = "جيد"
        elif overall_score >= 75:
            adequacy_status = "مقبول"
        else:
            adequacy_status = "غير كافي"
        
        return {
            'cow_id': cow.cow_id,
            'feed_mix_id': feed_mix.mix_id,
            'required_feed_kg': round(required_feed_kg, 2),
            'actual_intake': {k: round(v, 2) for k, v in actual_intake.items()},
            'adequacy_ratios': {k: round(v, 2) for k, v in adequacy_ratios.items()},
            'overall_score': round(overall_score, 1),
            'adequacy_status': adequacy_status,
            'deficiencies': deficiencies,
            'excesses': excesses,
            'daily_cost': round(feed_mix.cost_per_kg * required_feed_kg, 3)
        }
    
    def optimize_feed_mix(self, target_cows: List[Cow], available_ingredients: List[str],
                         max_cost_per_kg: float = 5.0) -> Dict:
        """تحسين خلطة العلف لمجموعة من الأبقار"""
        
        # حساب متوسط الاحتياجات
        total_requirements = {}
        total_weight = 0
        
        for cow in target_cows:
            cow_req = self.calculate_cow_requirements(cow)['requirements']
            cow_weight = cow.weight
            total_weight += cow_weight
            
            for nutrient, value in cow_req.items():
                if nutrient not in total_requirements:
                    total_requirements[nutrient] = 0
                total_requirements[nutrient] += value * cow_weight
        
        # حساب المتوسط المرجح
        avg_requirements = {}
        for nutrient, total_value in total_requirements.items():
            avg_requirements[nutrient] = total_value / total_weight
        
        # خوارزمية تحسين بسيطة (يمكن تطويرها لاحقاً)
        best_mix = self._simple_optimization(avg_requirements, available_ingredients, max_cost_per_kg)
        
        return {
            'target_cows_count': len(target_cows),
            'average_requirements': avg_requirements,
            'optimized_mix': best_mix,
            'optimization_method': 'simple_linear'
        }
    
    def _simple_optimization(self, requirements: Dict, ingredients: List[str], 
                           max_cost: float) -> Dict:
        """خوارزمية تحسين بسيطة"""
        from config.nutrition_standards import FEED_INGREDIENTS
        
        # هذه خوارزمية بسيطة - يمكن استبدالها بخوارزمية أكثر تعقيداً
        best_mix = {}
        
        # إضافة مصدر طاقة أساسي (ذرة)
        if 'corn' in ingredients:
            best_mix['corn'] = 40.0
        
        # إضافة مصدر بروتين (كسبة صويا)
        if 'soybean_meal' in ingredients:
            best_mix['soybean_meal'] = 20.0
        
        # إضافة مصدر ألياف (دريس برسيم)
        if 'alfalfa_hay' in ingredients:
            best_mix['alfalfa_hay'] = 30.0
        
        # إضافة مكونات أخرى
        remaining_percentage = 100 - sum(best_mix.values())
        if remaining_percentage > 0:
            for ingredient in ingredients:
                if ingredient not in best_mix and remaining_percentage > 0:
                    allocation = min(remaining_percentage, 10.0)
                    best_mix[ingredient] = allocation
                    remaining_percentage -= allocation
        
        return best_mix
    
    def generate_feeding_recommendations(self, cow: Cow, feed_mix: FeedMix) -> List[str]:
        """توليد توصيات التغذية"""
        
        adequacy_analysis = self.analyze_feed_adequacy(cow, feed_mix)
        recommendations = []
        
        # توصيات حسب النقص
        for deficiency in adequacy_analysis['deficiencies']:
            nutrient = deficiency['nutrient']
            deficit = deficiency['deficit_percent']
            
            if nutrient == 'crude_protein':
                recommendations.append(f"زيادة البروتين بنسبة {deficit}% - أضف كسبة فول الصويا")
            elif nutrient == 'energy_mcal':
                recommendations.append(f"زيادة الطاقة بنسبة {deficit}% - أضف الذرة أو الشعير")
            elif nutrient == 'fiber':
                recommendations.append(f"زيادة الألياف بنسبة {deficit}% - أضف دريس البرسيم")
            elif nutrient == 'calcium':
                recommendations.append(f"زيادة الكالسيوم بنسبة {deficit}% - أضف الحجر الجيري")
        
        # توصيات حسب الزيادة
        for excess in adequacy_analysis['excesses']:
            nutrient = excess['nutrient']
            excess_percent = excess['excess_percent']
            
            if excess_percent > 30:
                recommendations.append(f"تقليل {nutrient} - الزيادة {excess_percent}% قد تكون مكلفة")
        
        # توصيات حسب مرحلة الحليب
        if cow.lactation_stage == 'مبكرة':
            recommendations.append("مرحلة مبكرة: زيادة الطاقة والبروتين لدعم الإنتاج العالي")
        elif cow.lactation_stage == 'جافة':
            recommendations.append("فترة جفاف: تقليل الطاقة والتركيز على الألياف")
        
        # توصيات حسب حالة الجسم
        if cow.body_condition_score < 2.5:
            recommendations.append("حالة جسم منخفضة: زيادة الطاقة لتحسين الوزن")
        elif cow.body_condition_score > 3.5:
            recommendations.append("حالة جسم مرتفعة: تقليل الطاقة لتجنب السمنة")
        
        return recommendations
    
    def calculate_economic_efficiency(self, cow: Cow, feed_mix: FeedMix, 
                                    milk_price_per_liter: float = 2.5) -> Dict:
        """حساب الكفاءة الاقتصادية للتغذية"""
        
        adequacy_analysis = self.analyze_feed_adequacy(cow, feed_mix)
        daily_feed_cost = adequacy_analysis['daily_cost']
        
        # حساب الإيرادات
        daily_milk_revenue = cow.average_daily_milk * milk_price_per_liter
        
        # حساب الربح
        daily_profit = daily_milk_revenue - daily_feed_cost
        
        # حساب نسبة التكلفة إلى الإيراد
        cost_to_revenue_ratio = daily_feed_cost / daily_milk_revenue if daily_milk_revenue > 0 else 0
        
        # حساب الكفاءة (لتر حليب لكل ريال علف)
        feed_efficiency = cow.average_daily_milk / daily_feed_cost if daily_feed_cost > 0 else 0
        
        # تقييم الكفاءة
        if cost_to_revenue_ratio < 0.4:
            efficiency_rating = "ممتاز"
        elif cost_to_revenue_ratio < 0.5:
            efficiency_rating = "جيد"
        elif cost_to_revenue_ratio < 0.6:
            efficiency_rating = "مقبول"
        else:
            efficiency_rating = "ضعيف"
        
        return {
            'cow_id': cow.cow_id,
            'daily_feed_cost': round(daily_feed_cost, 3),
            'daily_milk_revenue': round(daily_milk_revenue, 2),
            'daily_profit': round(daily_profit, 2),
            'cost_to_revenue_ratio': round(cost_to_revenue_ratio, 3),
            'feed_efficiency': round(feed_efficiency, 2),
            'efficiency_rating': efficiency_rating,
            'monthly_profit': round(daily_profit * 30, 2),
            'yearly_profit': round(daily_profit * 365, 2)
        }
