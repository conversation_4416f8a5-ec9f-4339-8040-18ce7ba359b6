#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء حزمة التوزيع النهائية
"""

import os
import shutil
import zipfile
from pathlib import Path

def create_distribution_package():
    """إنشاء حزمة التوزيع"""
    print("📦 إنشاء حزمة التوزيع...")
    
    # إنشاء مجلد التوزيع
    dist_folder = Path("نظام_إدارة_مزرعة_الأبقار_v1.0")
    if dist_folder.exists():
        shutil.rmtree(dist_folder)
    dist_folder.mkdir()
    
    # نسخ الملف التنفيذي
    exe_file = Path("dist/نظام_إدارة_مزرعة_الأبقار.exe")
    if exe_file.exists():
        shutil.copy2(exe_file, dist_folder / "نظام_إدارة_مزرعة_الأبقار.exe")
        print("✅ تم نسخ الملف التنفيذي")
    else:
        print("❌ لم يتم العثور على الملف التنفيذي")
        return False
    
    # نسخ ملف التشغيل
    if Path("تشغيل_النظام.bat").exists():
        shutil.copy2("تشغيل_النظام.bat", dist_folder / "تشغيل_النظام.bat")
        print("✅ تم نسخ ملف التشغيل")
    
    # نسخ ملف README
    if Path("README.txt").exists():
        shutil.copy2("README.txt", dist_folder / "README.txt")
        print("✅ تم نسخ ملف README")
    
    # نسخ قاعدة البيانات التجريبية
    if Path("dairy_farm.db").exists():
        shutil.copy2("dairy_farm.db", dist_folder / "dairy_farm.db")
        print("✅ تم نسخ قاعدة البيانات التجريبية")
    
    # إنشاء مجلدات فرعية
    (dist_folder / "reports").mkdir(exist_ok=True)
    (dist_folder / "uploads").mkdir(exist_ok=True)
    (dist_folder / "backups").mkdir(exist_ok=True)
    print("✅ تم إنشاء المجلدات الفرعية")
    
    # إنشاء ملف معلومات الإصدار
    version_info = f"""نظام إدارة مزرعة الأبقار الحلوب
الإصدار: 1.0.0
تاريخ البناء: {Path().cwd().name}
حجم الملف: {exe_file.stat().st_size / (1024*1024):.1f} ميجابايت

الميزات:
- إدارة شاملة للقطيع
- تتبع إنتاج الحليب اليومي والشهري
- إدارة متقدمة لخلطات العلف
- تقارير PDF احترافية
- واجهة عربية كاملة
- دعم العملة الأردنية (JOD)
- قاعدة بيانات SQLite محلية
- نسخ احتياطية تلقائية

متطلبات النظام:
- Windows 10 أو أحدث
- 4 جيجابايت RAM
- 500 ميجابايت مساحة فارغة
- متصفح ويب حديث

طريقة التشغيل:
1. فك الضغط عن الملفات
2. اضغط مرتين على "تشغيل_النظام.bat"
3. انتظر حتى يفتح المتصفح تلقائياً
4. استمتع بإدارة مزرعتك!

الدعم الفني:
- تأكد من تشغيل النظام كمدير
- أغلق برامج مكافحة الفيروسات مؤقتاً عند التشغيل الأول
- تأكد من عدم استخدام المنفذ 5000 من برنامج آخر

تم التطوير بواسطة: فريق التطوير
الترخيص: للاستخدام الشخصي والتجاري
"""
    
    with open(dist_folder / "معلومات_الإصدار.txt", "w", encoding="utf-8") as f:
        f.write(version_info)
    print("✅ تم إنشاء ملف معلومات الإصدار")
    
    # إنشاء ملف ZIP للتوزيع
    zip_name = "نظام_إدارة_مزرعة_الأبقار_v1.0.zip"
    with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for file_path in dist_folder.rglob('*'):
            if file_path.is_file():
                arcname = file_path.relative_to(dist_folder.parent)
                zipf.write(file_path, arcname)
    
    print(f"✅ تم إنشاء ملف ZIP: {zip_name}")
    
    # عرض معلومات الحزمة
    zip_size = Path(zip_name).stat().st_size / (1024*1024)
    print(f"\n📊 معلومات الحزمة:")
    print(f"   📁 المجلد: {dist_folder}")
    print(f"   📦 ملف ZIP: {zip_name}")
    print(f"   📏 حجم ZIP: {zip_size:.1f} ميجابايت")
    print(f"   📄 عدد الملفات: {len(list(dist_folder.rglob('*')))}")
    
    return True

if __name__ == "__main__":
    if create_distribution_package():
        print("\n🎉 تم إنشاء حزمة التوزيع بنجاح!")
        print("📦 الحزمة جاهزة للتوزيع والتثبيت")
        print("🚀 يمكن الآن توزيع الملف على أي جهاز Windows")
    else:
        print("\n❌ فشل في إنشاء حزمة التوزيع")
