#!/bin/bash

# تعيين الترميز
export LANG=ar_SA.UTF-8
export LC_ALL=ar_SA.UTF-8

echo ""
echo "==============================================="
echo "🐄 نظام إدارة مزرعة الأبقار الحلوب"
echo "   Dairy Farm Management System"
echo "==============================================="
echo ""

# التحقق من وجود Python
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ خطأ: Python غير مثبت على النظام"
        echo "يرجى تثبيت Python 3.8 أو أحدث"
        echo "Ubuntu/Debian: sudo apt install python3 python3-pip python3-venv"
        echo "CentOS/RHEL: sudo yum install python3 python3-pip"
        echo "macOS: brew install python3"
        exit 1
    else
        PYTHON_CMD=python
    fi
else
    PYTHON_CMD=python3
fi

echo "✅ تم العثور على Python"

# التحقق من وجود pip
if ! command -v pip3 &> /dev/null; then
    if ! command -v pip &> /dev/null; then
        echo "❌ خطأ: pip غير متاح"
        echo "يرجى تثبيت pip"
        exit 1
    else
        PIP_CMD=pip
    fi
else
    PIP_CMD=pip3
fi

echo "✅ تم العثور على pip"

# التحقق من وجود البيئة الافتراضية
if [ ! -d "venv" ]; then
    echo "📦 إنشاء البيئة الافتراضية..."
    $PYTHON_CMD -m venv venv
    if [ $? -ne 0 ]; then
        echo "❌ فشل في إنشاء البيئة الافتراضية"
        exit 1
    fi
    echo "✅ تم إنشاء البيئة الافتراضية"
fi

# تفعيل البيئة الافتراضية
echo "🔄 تفعيل البيئة الافتراضية..."
source venv/bin/activate
if [ $? -ne 0 ]; then
    echo "❌ فشل في تفعيل البيئة الافتراضية"
    exit 1
fi

echo "✅ تم تفعيل البيئة الافتراضية"

# تثبيت المتطلبات
echo "📥 تثبيت المتطلبات..."
pip install -r requirements.txt --quiet
if [ $? -ne 0 ]; then
    echo "❌ فشل في تثبيت المتطلبات"
    echo "جاري المحاولة مرة أخرى..."
    pip install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "❌ فشل في تثبيت المتطلبات نهائياً"
        exit 1
    fi
fi

echo "✅ تم تثبيت المتطلبات بنجاح"

# تشغيل النظام
echo ""
echo "🚀 بدء تشغيل النظام..."
echo "📱 سيتم فتح النظام في المتصفح تلقائياً"
echo "🌐 أو يمكنك زيارة: http://localhost:5000"
echo ""
echo "⚠️  للإيقاف: اضغط Ctrl+C"
echo ""

python main.py

echo ""
echo "👋 تم إيقاف النظام"
