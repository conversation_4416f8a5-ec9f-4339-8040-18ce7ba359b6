{% extends "base.html" %}

{% block title %}التقارير والإحصائيات - نظام إدارة مزرعة الأبقار{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h2">
        <i class="fas fa-chart-bar text-primary"></i>
        التقارير والإحصائيات
    </h1>
    <div class="d-flex gap-2">
        <button type="button" class="btn btn-success" onclick="generateReport('daily')">
            <i class="fas fa-file-pdf"></i>
            تقرير يومي
        </button>
        <button type="button" class="btn btn-info" onclick="generateReport('weekly')">
            <i class="fas fa-file-excel"></i>
            تقرير أسبوعي
        </button>
    </div>
</div>

<!-- إحصائيات عامة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-primary">{{ herd_stats.get('total_cows', 0) }}</h3>
                <p class="mb-0">إجمالي الأبقار</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-success">{{ herd_stats.get('active_cows', 0) }}</h3>
                <p class="mb-0">الأبقار النشطة</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-info">{{ "%.1f"|format(herd_stats.get('herd_average_milk', 0)) }}</h3>
                <p class="mb-0">متوسط الإنتاج (لتر)</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-warning">{{ "%.1f"|format(herd_stats.get('average_weight', 0)) }}</h3>
                <p class="mb-0">متوسط الوزن (كغ)</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- رسم بياني للإنتاج الأسبوعي -->
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line"></i>
                    إنتاج الحليب - آخر 7 أيام
                </h5>
            </div>
            <div class="card-body">
                <canvas id="weeklyMilkChart" height="100"></canvas>
            </div>
        </div>
    </div>

    <!-- توزيع السلالات -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie"></i>
                    توزيع السلالات
                </h5>
            </div>
            <div class="card-body">
                {% if herd_stats.get('breed_distribution') %}
                <canvas id="breedChart" height="200"></canvas>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-cow fa-3x mb-3"></i>
                    <p>لا توجد بيانات</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- توزيع الحالة الصحية -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-heart"></i>
                    الحالة الصحية للقطيع
                </h5>
            </div>
            <div class="card-body">
                {% if herd_stats.get('health_distribution') %}
                <canvas id="healthChart" height="150"></canvas>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-heartbeat fa-3x mb-3"></i>
                    <p>لا توجد بيانات</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- تقارير سريعة -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-file-alt"></i>
                    تقارير سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-primary" onclick="generateReport('daily')">
                        <i class="fas fa-calendar-day"></i>
                        تقرير إنتاج اليوم
                    </button>
                    <button type="button" class="btn btn-outline-success" onclick="generateReport('herd')">
                        <i class="fas fa-cow"></i>
                        تقرير ملخص القطيع
                    </button>
                    <button type="button" class="btn btn-outline-info" onclick="exportData('excel')">
                        <i class="fas fa-file-excel"></i>
                        تصدير البيانات Excel
                    </button>
                    <button type="button" class="btn btn-outline-warning" onclick="exportData('csv')">
                        <i class="fas fa-file-csv"></i>
                        تصدير البيانات CSV
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات تفصيلية -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-table"></i>
                    إحصائيات تفصيلية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>إحصائيات السلالات</h6>
                        {% if herd_stats.get('breed_distribution') %}
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>السلالة</th>
                                        <th>العدد</th>
                                        <th>متوسط الإنتاج</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for breed in herd_stats.breed_distribution %}
                                    <tr>
                                        <td>{{ breed.breed }}</td>
                                        <td>{{ breed.count }}</td>
                                        <td>{{ "%.1f"|format(breed.get('avg_milk', 0)) }} لتر</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <p class="text-muted">لا توجد بيانات</p>
                        {% endif %}
                    </div>
                    
                    <div class="col-md-6">
                        <h6>إحصائيات الحالة الصحية</h6>
                        {% if herd_stats.get('health_distribution') %}
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>الحالة</th>
                                        <th>العدد</th>
                                        <th>النسبة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for health in herd_stats.health_distribution %}
                                    <tr>
                                        <td>{{ health.health_status }}</td>
                                        <td>{{ health.count }}</td>
                                        <td>{{ "%.1f"|format((health.count / herd_stats.total_cows * 100) if herd_stats.total_cows > 0 else 0) }}%</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <p class="text-muted">لا توجد بيانات</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// رسم بياني للإنتاج الأسبوعي
{% if weekly_data %}
const weeklyCtx = document.getElementById('weeklyMilkChart').getContext('2d');
const weeklyChart = new Chart(weeklyCtx, {
    type: 'line',
    data: {
        labels: [{% for day in weekly_data %}'{{ day.date.strftime("%m-%d") }}'{% if not loop.last %},{% endif %}{% endfor %}],
        datasets: [{
            label: 'إنتاج الحليب (لتر)',
            data: [{% for day in weekly_data %}{{ day.total_milk }}{% if not loop.last %},{% endif %}{% endfor %}],
            borderColor: '#2E86AB',
            backgroundColor: 'rgba(46, 134, 171, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: 'اتجاه إنتاج الحليب الأسبوعي'
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                title: {
                    display: true,
                    text: 'الكمية (لتر)'
                }
            }
        }
    }
});
{% endif %}

// رسم بياني لتوزيع السلالات
{% if herd_stats.get('breed_distribution') %}
const breedCtx = document.getElementById('breedChart').getContext('2d');
const breedChart = new Chart(breedCtx, {
    type: 'doughnut',
    data: {
        labels: [{% for breed in herd_stats.breed_distribution %}'{{ breed.breed }}'{% if not loop.last %},{% endif %}{% endfor %}],
        datasets: [{
            data: [{% for breed in herd_stats.breed_distribution %}{{ breed.count }}{% if not loop.last %},{% endif %}{% endfor %}],
            backgroundColor: [
                '#2E86AB',
                '#A23B72',
                '#F18F01',
                '#C73E1D',
                '#28a745'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
{% endif %}

// رسم بياني للحالة الصحية
{% if herd_stats.get('health_distribution') %}
const healthCtx = document.getElementById('healthChart').getContext('2d');
const healthChart = new Chart(healthCtx, {
    type: 'bar',
    data: {
        labels: [{% for health in herd_stats.health_distribution %}'{{ health.health_status }}'{% if not loop.last %},{% endif %}{% endfor %}],
        datasets: [{
            label: 'عدد الأبقار',
            data: [{% for health in herd_stats.health_distribution %}{{ health.count }}{% if not loop.last %},{% endif %}{% endfor %}],
            backgroundColor: [
                '#28a745',  // سليمة
                '#dc3545',  // مريضة
                '#17a2b8',  // حامل
                '#ffc107'   // جافة
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});
{% endif %}

// إنشاء التقارير
function generateReport(type) {
    const date = new Date().toISOString().split('T')[0];
    window.open(`/api/generate_report/${type}?date=${date}`, '_blank');
}

// تصدير البيانات
function exportData(format) {
    alert(`تصدير البيانات بصيغة ${format} - هذه الميزة قيد التطوير`);
}
</script>
{% endblock %}
