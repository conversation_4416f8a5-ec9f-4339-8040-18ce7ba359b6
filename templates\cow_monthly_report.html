{% extends "base.html" %}

{% block title %}تقرير {{ cow.name }} - {{ current_month }}/{{ current_year }}{% endblock %}

{% block extra_css %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<style>
    .cow-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .stat-card {
        background: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border-left: 4px solid #28a745;
    }
    
    .stat-card.warning {
        border-left-color: #ffc107;
    }
    
    .stat-card.danger {
        border-left-color: #dc3545;
    }
    
    .chart-container {
        background: white;
        border-radius: 15px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    
    .daily-record {
        background: white;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 10px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        border-left: 4px solid #17a2b8;
    }
    
    .missing-day {
        background: #f8f9fa;
        border-left-color: #6c757d;
        opacity: 0.7;
    }
    
    .best-day {
        border-left-color: #28a745;
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    }
    
    .worst-day {
        border-left-color: #dc3545;
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    }
    
    .trend-increasing {
        color: #28a745;
    }
    
    .trend-decreasing {
        color: #dc3545;
    }
    
    .trend-stable {
        color: #17a2b8;
    }
    
    .month-selector {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Cow Header -->
    <div class="cow-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="h2 mb-2">
                    <i class="fas fa-cow"></i>
                    {{ cow.name }}
                </h1>
                <p class="mb-1"><strong>رقم البقرة:</strong> {{ cow.cow_id }}</p>
                <p class="mb-1"><strong>السلالة:</strong> {{ cow.breed }}</p>
                <p class="mb-0"><strong>الفترة:</strong> {{ first_day.strftime('%d/%m/%Y') }} - {{ last_day.strftime('%d/%m/%Y') }}</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{{ url_for('monthly_milk_report', year=current_year, month=current_month) }}" class="btn btn-light btn-lg">
                    <i class="fas fa-arrow-right"></i>
                    العودة للتقرير العام
                </a>
            </div>
        </div>
    </div>

    <!-- Month Selector -->
    <div class="month-selector">
        <form method="GET" class="row g-3 align-items-end">
            <div class="col-md-4">
                <label for="year" class="form-label">السنة</label>
                <select name="year" id="year" class="form-select">
                    {% for y in range(2020, 2030) %}
                    <option value="{{ y }}" {% if y == current_year %}selected{% endif %}>{{ y }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-4">
                <label for="month" class="form-label">الشهر</label>
                <select name="month" id="month" class="form-select">
                    {% set months = [
                        (1, 'يناير'), (2, 'فبراير'), (3, 'مارس'), (4, 'أبريل'),
                        (5, 'مايو'), (6, 'يونيو'), (7, 'يوليو'), (8, 'أغسطس'),
                        (9, 'سبتمبر'), (10, 'أكتوبر'), (11, 'نوفمبر'), (12, 'ديسمبر')
                    ] %}
                    {% for month_num, month_name in months %}
                    <option value="{{ month_num }}" {% if month_num == current_month %}selected{% endif %}>{{ month_name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-4">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i>
                    عرض الشهر
                </button>
            </div>
        </form>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <h6 class="text-muted mb-2">إجمالي الإنتاج</h6>
                <h3 class="mb-0">{{ "%.1f"|format(cow_stats.total_milk) }} لتر</h3>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <h6 class="text-muted mb-2">المتوسط اليومي</h6>
                <h3 class="mb-0">{{ "%.1f"|format(cow_stats.average_daily) }} لتر</h3>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card {% if cow_stats.productive_days < cow_stats.total_days * 0.8 %}warning{% endif %}">
                <h6 class="text-muted mb-2">الأيام المنتجة</h6>
                <h3 class="mb-0">{{ cow_stats.productive_days }}/{{ cow_stats.total_days }}</h3>
                <small class="text-muted">{{ "%.1f"|format((cow_stats.productive_days / cow_stats.total_days * 100) if cow_stats.total_days > 0 else 0) }}%</small>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <h6 class="text-muted mb-2">الاتجاه</h6>
                <h3 class="mb-0">
                    {% if cow_stats.trend_analysis == 'increasing' %}
                    <i class="fas fa-arrow-up trend-increasing"></i>
                    متزايد
                    {% elif cow_stats.trend_analysis == 'decreasing' %}
                    <i class="fas fa-arrow-down trend-decreasing"></i>
                    متناقص
                    {% else %}
                    <i class="fas fa-minus trend-stable"></i>
                    مستقر
                    {% endif %}
                </h3>
            </div>
        </div>
    </div>

    <!-- Best and Worst Days -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="stat-card">
                <h6 class="text-muted mb-2">
                    <i class="fas fa-trophy text-warning"></i>
                    أفضل يوم إنتاج
                </h6>
                <h4 class="mb-1">{{ "%.1f"|format(cow_stats.best_day.total) }} لتر</h4>
                <p class="mb-1"><strong>التاريخ:</strong> {{ cow_stats.best_day.date }}</p>
                <small class="text-muted">
                    صباح: {{ "%.1f"|format(cow_stats.best_day.morning) }} لتر | 
                    مساء: {{ "%.1f"|format(cow_stats.best_day.evening) }} لتر
                </small>
            </div>
        </div>
        <div class="col-md-6">
            <div class="stat-card">
                <h6 class="text-muted mb-2">
                    <i class="fas fa-exclamation-triangle text-warning"></i>
                    أقل يوم إنتاج
                </h6>
                <h4 class="mb-1">{{ "%.1f"|format(cow_stats.worst_day.total) }} لتر</h4>
                <p class="mb-1"><strong>التاريخ:</strong> {{ cow_stats.worst_day.date }}</p>
                <small class="text-muted">
                    صباح: {{ "%.1f"|format(cow_stats.worst_day.morning) }} لتر | 
                    مساء: {{ "%.1f"|format(cow_stats.worst_day.evening) }} لتر
                </small>
            </div>
        </div>
    </div>

    <!-- Charts -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="chart-container">
                <h5 class="mb-3">
                    <i class="fas fa-chart-line text-primary"></i>
                    الإنتاج اليومي - {{ cow.name }}
                </h5>
                <canvas id="dailyChart" height="100"></canvas>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-12">
            <div class="chart-container">
                <h5 class="mb-3">
                    <i class="fas fa-chart-bar text-success"></i>
                    توزيع الإنتاج (صباح/مساء)
                </h5>
                <canvas id="distributionChart" height="100"></canvas>
            </div>
        </div>
    </div>

    <!-- Daily Records -->
    <div class="row">
        <div class="col-12">
            <div class="chart-container">
                <h5 class="mb-3">
                    <i class="fas fa-calendar-day text-info"></i>
                    السجل اليومي التفصيلي
                </h5>
                
                {% if cow_records %}
                <div class="row">
                    {% for record in cow_records %}
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="daily-record 
                            {% if record.record_date == cow_stats.best_day.date %}best-day{% endif %}
                            {% if record.record_date == cow_stats.worst_day.date %}worst-day{% endif %}">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="mb-0">{{ record.record_date }}</h6>
                                <span class="badge bg-primary">{{ "%.1f"|format(record.daily_total) }} لتر</span>
                            </div>
                            <div class="row text-center">
                                <div class="col-6">
                                    <small class="text-muted d-block">صباح</small>
                                    <strong>{{ "%.1f"|format(record.morning_milk) }}</strong>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted d-block">مساء</small>
                                    <strong>{{ "%.1f"|format(record.evening_milk) }}</strong>
                                </div>
                            </div>
                            {% if record.notes %}
                            <div class="mt-2">
                                <small class="text-muted">
                                    <i class="fas fa-sticky-note"></i>
                                    {{ record.notes }}
                                </small>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                    
                    <!-- Missing Days -->
                    {% for missing_date in cow_stats.missing_days %}
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="daily-record missing-day">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="mb-0">{{ missing_date }}</h6>
                                <span class="badge bg-secondary">لا يوجد سجل</span>
                            </div>
                            <div class="text-center">
                                <small class="text-muted">
                                    <i class="fas fa-exclamation-circle"></i>
                                    لم يتم تسجيل إنتاج لهذا اليوم
                                </small>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد سجلات حليب لهذه البقرة في هذا الشهر</h5>
                    <p class="text-muted">ابدأ بإضافة سجلات الحليب اليومية</p>
                    <a href="{{ url_for('milk_tracking') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        إضافة سجل حليب
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// بيانات الرسم البياني
const chartData = {{ chart_data|tojson }};

// رسم بياني للإنتاج اليومي
const dailyCtx = document.getElementById('dailyChart').getContext('2d');
new Chart(dailyCtx, {
    type: 'line',
    data: {
        labels: chartData.dates.map(date => {
            const d = new Date(date);
            return d.getDate() + '/' + (d.getMonth() + 1);
        }),
        datasets: [{
            label: 'الإنتاج الإجمالي',
            data: chartData.total_data,
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                title: {
                    display: true,
                    text: 'الكمية (لتر)'
                }
            }
        }
    }
});

// رسم بياني لتوزيع الإنتاج
const distributionCtx = document.getElementById('distributionChart').getContext('2d');
new Chart(distributionCtx, {
    type: 'bar',
    data: {
        labels: chartData.dates.map(date => {
            const d = new Date(date);
            return d.getDate() + '/' + (d.getMonth() + 1);
        }),
        datasets: [{
            label: 'حليب الصباح',
            data: chartData.morning_data,
            backgroundColor: '#28a745',
            borderWidth: 0
        }, {
            label: 'حليب المساء',
            data: chartData.evening_data,
            backgroundColor: '#17a2b8',
            borderWidth: 0
        }]
    },
    options: {
        responsive: true,
        scales: {
            x: {
                stacked: true
            },
            y: {
                stacked: true,
                beginAtZero: true,
                title: {
                    display: true,
                    text: 'الكمية (لتر)'
                }
            }
        }
    }
});
</script>
{% endblock %}
