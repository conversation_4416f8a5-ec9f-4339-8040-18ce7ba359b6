# -*- coding: utf-8 -*-
"""
واجهة إدارة القطيع - Herd Management Widget
إدارة الأبقار وبياناتها
"""

from datetime import date, datetime
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                            QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                            QComboBox, QDateEdit, QDoubleSpinBox, QTextEdit,
                            QGroupBox, QFormLayout, QMessageBox, QHeaderView,
                            QAbstractItemView, QMenu, QAction, QDialog,
                            QDialogButtonBox, QSpinBox, QCheckBox)
from PyQt5.QtCore import Qt, pyqtSignal, QDate
from PyQt5.QtGui import QFont, QColor

from models.cow import Cow
from config.nutrition_standards import BREED_WEIGHTS

class CowDialog(QDialog):
    """نافذة حوار إضافة/تعديل البقرة"""
    
    def __init__(self, cow=None, parent=None):
        super().__init__(parent)
        self.cow = cow
        self.is_edit_mode = cow is not None
        self.init_ui()
        if self.is_edit_mode:
            self.load_cow_data()
    
    def init_ui(self):
        """إعداد واجهة النافذة"""
        title = "تعديل البقرة" if self.is_edit_mode else "إضافة بقرة جديدة"
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(500, 600)
        
        layout = QVBoxLayout(self)
        
        # معلومات أساسية
        basic_group = QGroupBox("المعلومات الأساسية")
        basic_layout = QFormLayout(basic_group)
        
        self.cow_id_edit = QLineEdit()
        self.cow_id_edit.setEnabled(not self.is_edit_mode)  # لا يمكن تعديل الرقم
        basic_layout.addRow("رقم البقرة:", self.cow_id_edit)
        
        self.name_edit = QLineEdit()
        basic_layout.addRow("اسم البقرة:", self.name_edit)
        
        self.breed_combo = QComboBox()
        for breed_key, breed_info in BREED_WEIGHTS.items():
            self.breed_combo.addItem(breed_info['name_ar'], breed_key)
        basic_layout.addRow("السلالة:", self.breed_combo)
        
        self.birth_date_edit = QDateEdit()
        self.birth_date_edit.setDate(QDate.currentDate().addYears(-3))
        self.birth_date_edit.setCalendarPopup(True)
        basic_layout.addRow("تاريخ الميلاد:", self.birth_date_edit)
        
        self.weight_spin = QDoubleSpinBox()
        self.weight_spin.setRange(200, 1000)
        self.weight_spin.setValue(600)
        self.weight_spin.setSuffix(" كغ")
        basic_layout.addRow("الوزن:", self.weight_spin)
        
        layout.addWidget(basic_group)
        
        # معلومات الإنتاج
        production_group = QGroupBox("معلومات الإنتاج")
        production_layout = QFormLayout(production_group)
        
        self.last_calving_edit = QDateEdit()
        self.last_calving_edit.setDate(QDate.currentDate().addMonths(-2))
        self.last_calving_edit.setCalendarPopup(True)
        production_layout.addRow("آخر ولادة:", self.last_calving_edit)
        
        self.lactation_spin = QSpinBox()
        self.lactation_spin.setRange(1, 10)
        self.lactation_spin.setValue(1)
        production_layout.addRow("رقم الموسم:", self.lactation_spin)
        
        self.health_combo = QComboBox()
        health_statuses = ["سليمة", "مريضة", "حامل", "جافة"]
        self.health_combo.addItems(health_statuses)
        production_layout.addRow("الحالة الصحية:", self.health_combo)
        
        layout.addWidget(production_group)
        
        # معلومات التغذية
        feeding_group = QGroupBox("معلومات التغذية")
        feeding_layout = QFormLayout(feeding_group)
        
        self.feed_intake_spin = QDoubleSpinBox()
        self.feed_intake_spin.setRange(10, 40)
        self.feed_intake_spin.setValue(22)
        self.feed_intake_spin.setSuffix(" كغ/يوم")
        feeding_layout.addRow("استهلاك العلف:", self.feed_intake_spin)
        
        self.body_condition_spin = QDoubleSpinBox()
        self.body_condition_spin.setRange(1.0, 5.0)
        self.body_condition_spin.setValue(3.0)
        self.body_condition_spin.setDecimals(1)
        self.body_condition_spin.setSingleStep(0.1)
        feeding_layout.addRow("حالة الجسم (1-5):", self.body_condition_spin)
        
        layout.addWidget(feeding_group)
        
        # ملاحظات
        notes_group = QGroupBox("ملاحظات")
        notes_layout = QVBoxLayout(notes_group)
        
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(100)
        notes_layout.addWidget(self.notes_edit)
        
        layout.addWidget(notes_group)
        
        # حالة النشاط
        self.is_active_check = QCheckBox("بقرة نشطة")
        self.is_active_check.setChecked(True)
        layout.addWidget(self.is_active_check)
        
        # أزرار الحوار
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
    
    def load_cow_data(self):
        """تحميل بيانات البقرة للتعديل"""
        if not self.cow:
            return
        
        self.cow_id_edit.setText(self.cow.cow_id)
        self.name_edit.setText(self.cow.name)
        
        # تحديد السلالة
        for i in range(self.breed_combo.count()):
            if self.breed_combo.itemData(i) == self.cow.breed:
                self.breed_combo.setCurrentIndex(i)
                break
        
        self.birth_date_edit.setDate(QDate.fromString(self.cow.birth_date.isoformat(), Qt.ISODate))
        self.weight_spin.setValue(self.cow.weight)
        
        if self.cow.last_calving_date:
            self.last_calving_edit.setDate(QDate.fromString(self.cow.last_calving_date.isoformat(), Qt.ISODate))
        
        self.lactation_spin.setValue(self.cow.lactation_number)
        
        # تحديد الحالة الصحية
        health_index = self.health_combo.findText(self.cow.health_status)
        if health_index >= 0:
            self.health_combo.setCurrentIndex(health_index)
        
        self.feed_intake_spin.setValue(self.cow.daily_feed_intake)
        self.body_condition_spin.setValue(self.cow.body_condition_score)
        self.notes_edit.setPlainText(self.cow.notes)
        self.is_active_check.setChecked(self.cow.is_active)
    
    def get_cow_data(self):
        """الحصول على بيانات البقرة من النموذج"""
        cow_id = self.cow_id_edit.text().strip()
        if not cow_id:
            QMessageBox.warning(self, "خطأ", "يجب إدخال رقم البقرة")
            return None
        
        name = self.name_edit.text().strip() or f"بقرة رقم {cow_id}"
        breed = self.breed_combo.currentData()
        birth_date = self.birth_date_edit.date().toPyDate()
        weight = self.weight_spin.value()
        
        if self.is_edit_mode:
            cow = self.cow
            cow.name = name
            cow.breed = breed
            cow.birth_date = birth_date
            cow.weight = weight
        else:
            cow = Cow(cow_id, name, breed, birth_date, weight)
        
        cow.last_calving_date = self.last_calving_edit.date().toPyDate()
        cow.lactation_number = self.lactation_spin.value()
        cow.health_status = self.health_combo.currentText()
        cow.daily_feed_intake = self.feed_intake_spin.value()
        cow.body_condition_score = self.body_condition_spin.value()
        cow.notes = self.notes_edit.toPlainText()
        cow.is_active = self.is_active_check.isChecked()
        
        return cow

class HerdManagementWidget(QWidget):
    """واجهة إدارة القطيع"""
    
    data_changed = pyqtSignal()
    
    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.cows = []
        self.init_ui()
        self.load_data()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # شريط الأدوات
        toolbar_layout = QHBoxLayout()
        
        self.add_button = QPushButton("إضافة بقرة")
        self.add_button.clicked.connect(self.add_new_cow)
        toolbar_layout.addWidget(self.add_button)
        
        self.edit_button = QPushButton("تعديل")
        self.edit_button.clicked.connect(self.edit_cow)
        self.edit_button.setEnabled(False)
        toolbar_layout.addWidget(self.edit_button)
        
        self.delete_button = QPushButton("حذف")
        self.delete_button.clicked.connect(self.delete_cow)
        self.delete_button.setEnabled(False)
        toolbar_layout.addWidget(self.delete_button)
        
        toolbar_layout.addStretch()
        
        # مربع البحث
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("البحث برقم البقرة أو الاسم...")
        self.search_edit.textChanged.connect(self.filter_cows)
        toolbar_layout.addWidget(QLabel("بحث:"))
        toolbar_layout.addWidget(self.search_edit)
        
        # فلتر السلالة
        self.breed_filter = QComboBox()
        self.breed_filter.addItem("جميع السلالات", "")
        for breed_key, breed_info in BREED_WEIGHTS.items():
            self.breed_filter.addItem(breed_info['name_ar'], breed_key)
        self.breed_filter.currentTextChanged.connect(self.filter_cows)
        toolbar_layout.addWidget(QLabel("السلالة:"))
        toolbar_layout.addWidget(self.breed_filter)
        
        # فلتر الحالة
        self.status_filter = QComboBox()
        statuses = ["جميع الحالات", "سليمة", "مريضة", "حامل", "جافة"]
        self.status_filter.addItems(statuses)
        self.status_filter.currentTextChanged.connect(self.filter_cows)
        toolbar_layout.addWidget(QLabel("الحالة:"))
        toolbar_layout.addWidget(self.status_filter)
        
        layout.addLayout(toolbar_layout)
        
        # جدول الأبقار
        self.cows_table = QTableWidget()
        self.setup_table()
        layout.addWidget(self.cows_table)
        
        # معلومات سريعة
        info_layout = QHBoxLayout()
        
        self.total_cows_label = QLabel("إجمالي الأبقار: 0")
        self.active_cows_label = QLabel("النشطة: 0")
        self.avg_milk_label = QLabel("متوسط الإنتاج: 0.0 لتر")
        
        info_layout.addWidget(self.total_cows_label)
        info_layout.addWidget(self.active_cows_label)
        info_layout.addWidget(self.avg_milk_label)
        info_layout.addStretch()
        
        layout.addLayout(info_layout)
    
    def setup_table(self):
        """إعداد جدول الأبقار"""
        headers = ["رقم البقرة", "الاسم", "السلالة", "العمر", "الوزن", 
                  "آخر ولادة", "أيام الحليب", "متوسط الإنتاج", "الحالة", "نشطة"]
        
        self.cows_table.setColumnCount(len(headers))
        self.cows_table.setHorizontalHeaderLabels(headers)
        
        # إعداد خصائص الجدول
        self.cows_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.cows_table.setAlternatingRowColors(True)
        self.cows_table.setSortingEnabled(True)
        
        # تمديد الأعمدة
        header = self.cows_table.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(headers)):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)
        
        # ربط الأحداث
        self.cows_table.selectionModel().selectionChanged.connect(self.on_selection_changed)
        self.cows_table.doubleClicked.connect(self.edit_cow)
        
        # قائمة السياق
        self.cows_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.cows_table.customContextMenuRequested.connect(self.show_context_menu)
    
    def load_data(self):
        """تحميل بيانات الأبقار"""
        try:
            self.cows = self.db_manager.get_all_cows(active_only=False)
            self.populate_table()
            self.update_info_labels()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل البيانات: {str(e)}")
    
    def populate_table(self):
        """ملء الجدول بالبيانات"""
        filtered_cows = self.get_filtered_cows()
        
        self.cows_table.setRowCount(len(filtered_cows))
        
        for row, cow in enumerate(filtered_cows):
            # رقم البقرة
            self.cows_table.setItem(row, 0, QTableWidgetItem(cow.cow_id))
            
            # الاسم
            self.cows_table.setItem(row, 1, QTableWidgetItem(cow.name))
            
            # السلالة
            breed_name = BREED_WEIGHTS.get(cow.breed, {}).get('name_ar', cow.breed)
            self.cows_table.setItem(row, 2, QTableWidgetItem(breed_name))
            
            # العمر
            age_text = f"{cow.age_in_years:.1f} سنة"
            self.cows_table.setItem(row, 3, QTableWidgetItem(age_text))
            
            # الوزن
            weight_text = f"{cow.weight:.0f} كغ"
            self.cows_table.setItem(row, 4, QTableWidgetItem(weight_text))
            
            # آخر ولادة
            calving_text = cow.last_calving_date.strftime("%Y-%m-%d") if cow.last_calving_date else "غير محدد"
            self.cows_table.setItem(row, 5, QTableWidgetItem(calving_text))
            
            # أيام الحليب
            self.cows_table.setItem(row, 6, QTableWidgetItem(str(cow.days_in_milk)))
            
            # متوسط الإنتاج
            milk_text = f"{cow.average_daily_milk:.1f} لتر"
            self.cows_table.setItem(row, 7, QTableWidgetItem(milk_text))
            
            # الحالة الصحية
            status_item = QTableWidgetItem(cow.health_status)
            if cow.health_status == "مريضة":
                status_item.setBackground(QColor(255, 200, 200))
            elif cow.health_status == "حامل":
                status_item.setBackground(QColor(200, 255, 200))
            self.cows_table.setItem(row, 8, status_item)
            
            # نشطة
            active_text = "نعم" if cow.is_active else "لا"
            active_item = QTableWidgetItem(active_text)
            if not cow.is_active:
                active_item.setBackground(QColor(220, 220, 220))
            self.cows_table.setItem(row, 9, active_item)
    
    def get_filtered_cows(self):
        """الحصول على الأبقار المفلترة"""
        filtered_cows = self.cows
        
        # فلتر البحث
        search_text = self.search_edit.text().lower()
        if search_text:
            filtered_cows = [cow for cow in filtered_cows 
                           if search_text in cow.cow_id.lower() or 
                              search_text in cow.name.lower()]
        
        # فلتر السلالة
        breed_filter = self.breed_filter.currentData()
        if breed_filter:
            filtered_cows = [cow for cow in filtered_cows if cow.breed == breed_filter]
        
        # فلتر الحالة
        status_filter = self.status_filter.currentText()
        if status_filter != "جميع الحالات":
            filtered_cows = [cow for cow in filtered_cows if cow.health_status == status_filter]
        
        return filtered_cows
    
    def filter_cows(self):
        """تطبيق الفلاتر"""
        self.populate_table()
    
    def update_info_labels(self):
        """تحديث تسميات المعلومات"""
        total_cows = len(self.cows)
        active_cows = len([cow for cow in self.cows if cow.is_active])
        
        if active_cows > 0:
            avg_milk = sum(cow.average_daily_milk for cow in self.cows if cow.is_active) / active_cows
        else:
            avg_milk = 0.0
        
        self.total_cows_label.setText(f"إجمالي الأبقار: {total_cows}")
        self.active_cows_label.setText(f"النشطة: {active_cows}")
        self.avg_milk_label.setText(f"متوسط الإنتاج: {avg_milk:.1f} لتر")
    
    def on_selection_changed(self):
        """تغيير التحديد في الجدول"""
        has_selection = len(self.cows_table.selectedItems()) > 0
        self.edit_button.setEnabled(has_selection)
        self.delete_button.setEnabled(has_selection)
    
    def get_selected_cow(self):
        """الحصول على البقرة المحددة"""
        current_row = self.cows_table.currentRow()
        if current_row < 0:
            return None
        
        cow_id_item = self.cows_table.item(current_row, 0)
        if not cow_id_item:
            return None
        
        cow_id = cow_id_item.text()
        return next((cow for cow in self.cows if cow.cow_id == cow_id), None)
    
    def add_new_cow(self):
        """إضافة بقرة جديدة"""
        dialog = CowDialog(parent=self)
        if dialog.exec_() == QDialog.Accepted:
            cow_data = dialog.get_cow_data()
            if cow_data:
                try:
                    if self.db_manager.add_cow(cow_data):
                        QMessageBox.information(self, "نجح", "تم إضافة البقرة بنجاح")
                        self.load_data()
                        self.data_changed.emit()
                    else:
                        QMessageBox.warning(self, "خطأ", "فشل في إضافة البقرة - قد يكون الرقم مستخدم")
                except Exception as e:
                    QMessageBox.critical(self, "خطأ", f"خطأ في إضافة البقرة: {str(e)}")
    
    def edit_cow(self):
        """تعديل البقرة المحددة"""
        cow = self.get_selected_cow()
        if not cow:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد بقرة للتعديل")
            return
        
        dialog = CowDialog(cow, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            cow_data = dialog.get_cow_data()
            if cow_data:
                try:
                    if self.db_manager.update_cow(cow_data):
                        QMessageBox.information(self, "نجح", "تم تحديث البقرة بنجاح")
                        self.load_data()
                        self.data_changed.emit()
                    else:
                        QMessageBox.warning(self, "خطأ", "فشل في تحديث البقرة")
                except Exception as e:
                    QMessageBox.critical(self, "خطأ", f"خطأ في تحديث البقرة: {str(e)}")
    
    def delete_cow(self):
        """حذف البقرة المحددة"""
        cow = self.get_selected_cow()
        if not cow:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد بقرة للحذف")
            return
        
        reply = QMessageBox.question(self, "تأكيد الحذف", 
                                   f"هل تريد حذف البقرة {cow.name} ({cow.cow_id})؟\n"
                                   "سيتم حذف جميع سجلات الحليب المرتبطة بها.",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            try:
                # تعطيل البقرة بدلاً من حذفها
                cow.is_active = False
                if self.db_manager.update_cow(cow):
                    QMessageBox.information(self, "نجح", "تم تعطيل البقرة بنجاح")
                    self.load_data()
                    self.data_changed.emit()
                else:
                    QMessageBox.warning(self, "خطأ", "فشل في تعطيل البقرة")
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في تعطيل البقرة: {str(e)}")
    
    def show_context_menu(self, position):
        """عرض قائمة السياق"""
        if self.cows_table.itemAt(position) is None:
            return
        
        menu = QMenu()
        
        edit_action = QAction("تعديل", self)
        edit_action.triggered.connect(self.edit_cow)
        menu.addAction(edit_action)
        
        delete_action = QAction("حذف", self)
        delete_action.triggered.connect(self.delete_cow)
        menu.addAction(delete_action)
        
        menu.addSeparator()
        
        view_details_action = QAction("عرض التفاصيل", self)
        view_details_action.triggered.connect(self.view_cow_details)
        menu.addAction(view_details_action)
        
        menu.exec_(self.cows_table.mapToGlobal(position))
    
    def view_cow_details(self):
        """عرض تفاصيل البقرة"""
        cow = self.get_selected_cow()
        if not cow:
            return
        
        details = f"""
        رقم البقرة: {cow.cow_id}
        الاسم: {cow.name}
        السلالة: {BREED_WEIGHTS.get(cow.breed, {}).get('name_ar', cow.breed)}
        العمر: {cow.age_in_years:.1f} سنة
        الوزن: {cow.weight:.0f} كغ
        آخر ولادة: {cow.last_calving_date.strftime('%Y-%m-%d') if cow.last_calving_date else 'غير محدد'}
        أيام الحليب: {cow.days_in_milk}
        مرحلة الحليب: {cow.lactation_stage}
        متوسط الإنتاج: {cow.average_daily_milk:.1f} لتر/يوم
        إجمالي الموسم: {cow.total_lactation_milk:.1f} لتر
        الحالة الصحية: {cow.health_status}
        استهلاك العلف: {cow.daily_feed_intake:.1f} كغ/يوم
        حالة الجسم: {cow.body_condition_score:.1f}/5
        ملاحظات: {cow.notes or 'لا توجد ملاحظات'}
        """
        
        QMessageBox.information(self, f"تفاصيل البقرة {cow.name}", details)
    
    def refresh_data(self):
        """تحديث البيانات"""
        self.load_data()
