#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل سريع لنظام إدارة مزرعة الأبقار الحلوب
Quick start for Dairy Farm Management System
"""

import os
import sys
import webbrowser
import threading
import time

def check_requirements():
    """التحقق من المتطلبات الأساسية"""
    print("🔍 التحقق من المتطلبات...")
    
    required_modules = ['flask', 'pandas', 'matplotlib']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print(f"❌ مكتبات مفقودة: {', '.join(missing_modules)}")
        print("تثبيت المتطلبات: pip install -r requirements.txt")
        return False
    
    print("✅ جميع المتطلبات متوفرة")
    return True

def open_browser_delayed():
    """فتح المتصفح بعد تأخير"""
    time.sleep(3)
    try:
        webbrowser.open('http://localhost:5000')
    except:
        pass

def main():
    """تشغيل النظام"""
    print("=" * 60)
    print("🐄 نظام إدارة مزرعة الأبقار الحلوب")
    print("   Dairy Farm Management System")
    print("=" * 60)
    
    # التحقق من المتطلبات
    if not check_requirements():
        input("اضغط Enter للخروج...")
        return
    
    # التحقق من وجود قاعدة البيانات
    if not os.path.exists('dairy_farm.db'):
        print("\n📊 إنشاء قاعدة بيانات جديدة...")
        try:
            from database.db_manager import DatabaseManager
            db_manager = DatabaseManager()
            print("✅ تم إنشاء قاعدة البيانات")
        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
            input("اضغط Enter للخروج...")
            return
    
    print("\n🚀 بدء تشغيل الخادم...")
    print("🌐 العنوان: http://localhost:5000")
    print("📱 سيتم فتح المتصفح تلقائياً")
    print("\n⚠️  للإيقاف: اضغط Ctrl+C")
    print("-" * 60)
    
    # فتح المتصفح في خيط منفصل
    browser_thread = threading.Thread(target=open_browser_delayed)
    browser_thread.daemon = True
    browser_thread.start()
    
    # تشغيل التطبيق
    try:
        from app import app
        app.run(debug=False, host='0.0.0.0', port=5000, use_reloader=False)
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف النظام بنجاح")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {e}")
        print("جرب: pip install -r requirements.txt")
    
    input("\nاضغط Enter للخروج...")

if __name__ == '__main__':
    main()
