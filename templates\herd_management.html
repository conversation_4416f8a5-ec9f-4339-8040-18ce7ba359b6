{% extends "base.html" %}

{% block title %}إدارة القطيع - نظام إدارة مزرعة الأبقار{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h2">
        <i class="fas fa-cow text-primary"></i>
        إدارة القطيع
    </h1>
    <a href="{{ url_for('add_cow') }}" class="btn btn-primary">
        <i class="fas fa-plus"></i>
        إضافة بقرة جديدة
    </a>
</div>

<!-- فلاتر البحث -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">البحث</label>
                <input type="text" class="form-control" id="search" name="search" 
                       placeholder="رقم البقرة أو الاسم..." value="{{ search_query }}">
            </div>
            <div class="col-md-3">
                <label for="breed" class="form-label">السلالة</label>
                <select class="form-select" id="breed" name="breed">
                    <option value="">جميع السلالات</option>
                    {% for breed_key, breed_info in breed_weights.items() %}
                    <option value="{{ breed_key }}" {% if breed_filter == breed_key %}selected{% endif %}>
                        {{ breed_info.name_ar }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">الحالة الصحية</label>
                <select class="form-select" id="status" name="status">
                    <option value="">جميع الحالات</option>
                    <option value="سليمة" {% if status_filter == 'سليمة' %}selected{% endif %}>سليمة</option>
                    <option value="مريضة" {% if status_filter == 'مريضة' %}selected{% endif %}>مريضة</option>
                    <option value="حامل" {% if status_filter == 'حامل' %}selected{% endif %}>حامل</option>
                    <option value="جافة" {% if status_filter == 'جافة' %}selected{% endif %}>جافة</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-primary">{{ cows|length }}</h3>
                <p class="mb-0">إجمالي الأبقار</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-success">{{ cows|selectattr('is_active')|list|length }}</h3>
                <p class="mb-0">الأبقار النشطة</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-info">{{ "%.1f"|format((cows|selectattr('is_active')|map(attribute='average_daily_milk')|sum) / (cows|selectattr('is_active')|list|length) if cows|selectattr('is_active')|list|length > 0 else 0) }}</h3>
                <p class="mb-0">متوسط الإنتاج (لتر)</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-warning">{{ cows|selectattr('health_status', 'equalto', 'مريضة')|list|length }}</h3>
                <p class="mb-0">الأبقار المريضة</p>
            </div>
        </div>
    </div>
</div>

<!-- جدول الأبقار -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list"></i>
            قائمة الأبقار
        </h5>
    </div>
    <div class="card-body">
        {% if cows %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>رقم البقرة</th>
                        <th>الاسم</th>
                        <th>السلالة</th>
                        <th>العمر</th>
                        <th>الوزن</th>
                        <th>آخر ولادة</th>
                        <th>أيام الحليب</th>
                        <th>متوسط الإنتاج</th>
                        <th>الحالة الصحية</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for cow in cows %}
                    <tr {% if not cow.is_active %}class="table-secondary"{% endif %}>
                        <td>
                            <strong>{{ cow.cow_id }}</strong>
                        </td>
                        <td>{{ cow.name }}</td>
                        <td>
                            <span class="badge bg-info">
                                {{ breed_weights.get(cow.breed, {}).get('name_ar', cow.breed) }}
                            </span>
                        </td>
                        <td>{{ "%.1f"|format(cow.age_in_years) }} سنة</td>
                        <td>{{ "%.0f"|format(cow.weight) }} كغ</td>
                        <td>
                            {% if cow.last_calving_date %}
                                {{ cow.last_calving_date.strftime('%Y-%m-%d') }}
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-secondary">{{ cow.days_in_milk }}</span>
                        </td>
                        <td>
                            <span class="badge bg-success">{{ "%.1f"|format(cow.average_daily_milk) }} لتر</span>
                        </td>
                        <td>
                            {% if cow.health_status == 'سليمة' %}
                                <span class="badge bg-success">{{ cow.health_status }}</span>
                            {% elif cow.health_status == 'مريضة' %}
                                <span class="badge bg-danger">{{ cow.health_status }}</span>
                            {% elif cow.health_status == 'حامل' %}
                                <span class="badge bg-info">{{ cow.health_status }}</span>
                            {% else %}
                                <span class="badge bg-warning">{{ cow.health_status }}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if cow.is_active %}
                                <span class="badge bg-success">نشطة</span>
                            {% else %}
                                <span class="badge bg-secondary">معطلة</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('edit_cow', cow_id=cow.cow_id) }}" 
                                   class="btn btn-outline-primary" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="btn btn-outline-info" 
                                        onclick="showCowDetails('{{ cow.cow_id }}')" title="التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </button>
                                {% if cow.is_active %}
                                <form method="POST" action="{{ url_for('delete_cow', cow_id=cow.cow_id) }}" 
                                      style="display: inline;" onsubmit="return confirmDelete('هل تريد تعطيل هذه البقرة؟')">
                                    <button type="submit" class="btn btn-outline-danger" title="تعطيل">
                                        <i class="fas fa-ban"></i>
                                    </button>
                                </form>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-cow fa-5x text-muted mb-3"></i>
            <h4 class="text-muted">لا توجد أبقار</h4>
            <p class="text-muted">لم يتم العثور على أبقار تطابق معايير البحث</p>
            <a href="{{ url_for('add_cow') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                إضافة بقرة جديدة
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- نافذة تفاصيل البقرة -->
<div class="modal fade" id="cowDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل البقرة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="cowDetailsContent">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// عرض تفاصيل البقرة
function showCowDetails(cowId) {
    const modal = new bootstrap.Modal(document.getElementById('cowDetailsModal'));
    const content = document.getElementById('cowDetailsContent');
    
    // إظهار مؤشر التحميل
    content.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
        </div>
    `;
    
    modal.show();
    
    // البحث عن البقرة في البيانات المحملة
    const cows = {{ cows_data|tojson }};
    const cow = cows.find(c => c.cow_id === cowId);
    
    if (cow) {
        content.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6>المعلومات الأساسية</h6>
                    <table class="table table-sm">
                        <tr><td><strong>رقم البقرة:</strong></td><td>${cow.cow_id}</td></tr>
                        <tr><td><strong>الاسم:</strong></td><td>${cow.name}</td></tr>
                        <tr><td><strong>السلالة:</strong></td><td>${cow.breed}</td></tr>
                        <tr><td><strong>العمر:</strong></td><td>${cow.age_in_years.toFixed(1)} سنة</td></tr>
                        <tr><td><strong>الوزن:</strong></td><td>${cow.weight.toFixed(0)} كغ</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6>معلومات الإنتاج</h6>
                    <table class="table table-sm">
                        <tr><td><strong>آخر ولادة:</strong></td><td>${cow.last_calving_date || 'غير محدد'}</td></tr>
                        <tr><td><strong>أيام الحليب:</strong></td><td>${cow.days_in_milk}</td></tr>
                        <tr><td><strong>مرحلة الحليب:</strong></td><td>${cow.lactation_stage}</td></tr>
                        <tr><td><strong>متوسط الإنتاج:</strong></td><td>${cow.average_daily_milk.toFixed(1)} لتر</td></tr>
                        <tr><td><strong>إجمالي الموسم:</strong></td><td>${cow.total_lactation_milk.toFixed(1)} لتر</td></tr>
                    </table>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-6">
                    <h6>الحالة الصحية والتغذية</h6>
                    <table class="table table-sm">
                        <tr><td><strong>الحالة الصحية:</strong></td><td><span class="badge bg-${cow.health_status === 'سليمة' ? 'success' : cow.health_status === 'مريضة' ? 'danger' : 'warning'}">${cow.health_status}</span></td></tr>
                        <tr><td><strong>استهلاك العلف:</strong></td><td>${cow.daily_feed_intake.toFixed(1)} كغ/يوم</td></tr>
                        <tr><td><strong>حالة الجسم:</strong></td><td>${cow.body_condition_score.toFixed(1)}/5</td></tr>
                        <tr><td><strong>الحالة:</strong></td><td><span class="badge bg-${cow.is_active ? 'success' : 'secondary'}">${cow.is_active ? 'نشطة' : 'معطلة'}</span></td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6>ملاحظات</h6>
                    <div class="bg-light p-3 rounded">
                        ${cow.notes || 'لا توجد ملاحظات'}
                    </div>
                </div>
            </div>
        `;
    } else {
        content.innerHTML = '<div class="alert alert-danger">خطأ في تحميل بيانات البقرة</div>';
    }
}

// تحديث الجدول عند تغيير الفلاتر
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('search');
    const breedSelect = document.getElementById('breed');
    const statusSelect = document.getElementById('status');
    
    // تحديث تلقائي عند الكتابة في البحث
    let searchTimeout;
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(function() {
            document.querySelector('form').submit();
        }, 500);
    });
});
</script>
{% endblock %}
