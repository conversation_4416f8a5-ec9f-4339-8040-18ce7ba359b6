Metadata-Version: 2.1
Name: python-bidi
Version: 0.4.2
Summary: Pure python implementation of the BiDi layout algorithm
Home-page: https://github.com/MeirKriheli/python-bidi
Author: <PERSON><PERSON>
Author-email: <EMAIL>
License: http://www.gnu.org/licenses/lgpl.html
Keywords: bidi unicode layout
Platform: UNKNOWN
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: Operating System :: OS Independent
Classifier: License :: OSI Approved :: GNU Library or Lesser General Public License (LGPL)
Classifier: Topic :: Text Processing
Classifier: Programming Language :: Python :: 2
Classifier: Programming Language :: Python :: 2.6
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.3
Classifier: Programming Language :: Python :: 3.4
Classifier: Programming Language :: Python :: Implementation :: PyPy
Requires-Dist: six

===============================
Python BiDi
===============================

.. image:: https://badge.fury.io/py/python-bidi.png
    :target: http://badge.fury.io/py/python-bidi

.. image:: https://travis-ci.org/MeirKriheli/python-bidi.png?branch=master
        :target: https://travis-ci.org/MeirKriheli/python-bidi

`Bi-directional`_ (BiDi) layout implementation in pure python

`Package documentation`_

.. _Bi-directional: http://en.wikipedia.org/wiki/Bi-directional_text
.. _Package documentation: http://python-bidi.readthedocs.org/en/latest/

API
----

The algorithm starts with a single entry point `bidi.algorithm.get_display`.

**Required arguments:**

* ``unicode_or_str``: The original unicode or string (i.e.: storage). If it's a string
  use the optional argument ``encoding`` to specify it's encoding.

**Optional arguments:**

* ``encoding``: If unicode_or_str is a string, specifies the encoding. The
  algorithm uses unicodedata_ which requires unicode. This encoding will be
  used to decode and encode back to string before returning
  (default: "utf-8").

* ``upper_is_rtl``: True to treat upper case chars as strong 'R' for
  debugging (default: False).

* ``base_dir``:  'L' or 'R', override the calculated base_level.

* ``debug``: True to display (using `sys.stderr`_) the steps taken with the
  algorithm (default: False).

Returns the display layout, either as unicode or ``encoding`` encoded string
(depending on the type of ``unicode_or_str'``).

.. _unicodedata: http://docs.python.org/library/unicodedata.html
.. _sys.stderr: http://docs.python.org/library/sys.html?highlight=sys.stderr#sys.stderr

Example::

    >>> from bidi.algorithm import get_display
    >>> get_display(u'car is THE CAR in arabic', upper_is_rtl=True)
    u'car is RAC EHT in arabic'


CLI
----

``pybidi`` is a command line utility (calling  ``bidi.main``) for running the
bidi algorithm. the script can get a string as a parameter or read text from
`stdin`. Usage::

    $ pybidi -h
    Usage: pybidi [options]

    Options:
      -h, --help            show this help message and exit
      -e ENCODING, --encoding=ENCODING
                            Text encoding (default: utf-8)
      -u, --upper-is-rtl    treat upper case chars as strong 'R' for debugging
                            (default: False).
      -d, --debug           Output to stderr steps taken with the algorithm
      -b BASE_DIR, --base-dir=BASE_DIR
                            Override base direction [L|R]


Examples::

    $ pybidi -u 'car is THE CAR in arabic'
    car is RAC EHT in arabic

    $ cat ~/Documents/example.txt | pybidi
    ...

Installation
-------------

See ``docs/INSTALL.rst``

Running tests
--------------

To run the tests::

    python setup.py test

Some explicit tests are failing right now (see TODO)





0.4.2
-----

* Type Fixes, thanks jwilk


History
=========

0.4.1
-----

* Merged Fix for mixed RTL and numbers, Thanks Just van Rossum

0.4.0
-----

* Move to cookiecutter template
* Python 3 support (py2.6, 2.7, 3.3, 3.4 and pypy)
* Better docs
* Travis integration
* Tox tests
* PEP8 cleanup

0.3.4
------

* Remove extra newline in console script output

0.3.3
------

* Implement overriding base paragraph direction
* Allow overriding base direction in pybidi console script
* Fix returning display in same encoding

0.3.2
------

* Test for surrogate pairs
* Fix indentation in documentations
* Specify license in setup.py

0.3.1
-----

* Added missing description
* docs/INSTALL.rst

0.3
---

* Apply bidi mirroring
* Move to back function based implementation

0.2
---

* Move the algorithm to a class based implementation

0.1
---

* Initial release


