{% extends "base.html" %}

{% block title %}إضافة بقرة جديدة - نظام إدارة مزرعة الأبقار{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h2">
        <i class="fas fa-plus-circle text-primary"></i>
        إضافة بقرة جديدة
    </h1>
    <a href="{{ url_for('herd_management') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-right"></i>
        العودة للقائمة
    </a>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <form method="POST" class="needs-validation" novalidate>
            <!-- المعلومات الأساسية -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle"></i>
                        المعلومات الأساسية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="cow_id" class="form-label">رقم البقرة <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="cow_id" name="cow_id" required
                                   placeholder="مثال: C001">
                            <div class="invalid-feedback">
                                يرجى إدخال رقم البقرة
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">اسم البقرة</label>
                            <input type="text" class="form-control" id="name" name="name"
                                   placeholder="سيتم إنشاؤه تلقائياً إذا ترك فارغاً">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="breed" class="form-label">السلالة <span class="text-danger">*</span></label>
                            <select class="form-select" id="breed" name="breed" required>
                                <option value="">اختر السلالة</option>
                                {% for breed_key, breed_info in breed_weights.items() %}
                                <option value="{{ breed_key }}" data-weight="{{ breed_info.avg_weight }}" data-milk="{{ breed_info.milk_potential }}">
                                    {{ breed_info.name_ar }}
                                </option>
                                {% endfor %}
                            </select>
                            <div class="invalid-feedback">
                                يرجى اختيار السلالة
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="birth_date" class="form-label">تاريخ الميلاد <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="birth_date" name="birth_date" required>
                            <div class="invalid-feedback">
                                يرجى إدخال تاريخ الميلاد
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="weight" class="form-label">الوزن (كغ) <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="weight" name="weight" 
                                   min="200" max="1000" step="0.1" value="600" required>
                            <div class="form-text">الوزن بالكيلوغرام (200-1000 كغ)</div>
                            <div class="invalid-feedback">
                                يرجى إدخال وزن صحيح (200-1000 كغ)
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="registration_date" class="form-label">تاريخ التسجيل</label>
                            <input type="date" class="form-control" id="registration_date" name="registration_date"
                                   readonly>
                            <div class="form-text">تاريخ إضافة البقرة للنظام</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات الإنتاج -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-tint"></i>
                        معلومات الإنتاج
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="last_calving_date" class="form-label">تاريخ آخر ولادة</label>
                            <input type="date" class="form-control" id="last_calving_date" name="last_calving_date">
                            <div class="form-text">اتركه فارغاً إذا لم تلد بعد</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="lactation_number" class="form-label">رقم الموسم</label>
                            <select class="form-select" id="lactation_number" name="lactation_number">
                                <option value="1" selected>الموسم الأول</option>
                                <option value="2">الموسم الثاني</option>
                                <option value="3">الموسم الثالث</option>
                                <option value="4">الموسم الرابع</option>
                                <option value="5">الموسم الخامس</option>
                                <option value="6">الموسم السادس</option>
                                <option value="7">الموسم السابع</option>
                                <option value="8">الموسم الثامن</option>
                                <option value="9">الموسم التاسع</option>
                                <option value="10">الموسم العاشر</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="health_status" class="form-label">الحالة الصحية</label>
                            <select class="form-select" id="health_status" name="health_status">
                                <option value="سليمة" selected>سليمة</option>
                                <option value="مريضة">مريضة</option>
                                <option value="حامل">حامل</option>
                                <option value="جافة">جافة</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="form-check mt-4">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                                <label class="form-check-label" for="is_active">
                                    بقرة نشطة
                                </label>
                                <div class="form-text">البقرة النشطة تظهر في التقارير والإحصائيات</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات التغذية -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-seedling"></i>
                        معلومات التغذية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="daily_feed_intake" class="form-label">استهلاك العلف اليومي (كغ)</label>
                            <input type="number" class="form-control" id="daily_feed_intake" name="daily_feed_intake" 
                                   min="10" max="40" step="0.1" value="22.0">
                            <div class="form-text">كمية المادة الجافة المستهلكة يومياً</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="body_condition_score" class="form-label">حالة الجسم (1-5)</label>
                            <input type="number" class="form-control" id="body_condition_score" name="body_condition_score" 
                                   min="1" max="5" step="0.1" value="3.0">
                            <div class="form-text">تقييم حالة الجسم من 1 (نحيف جداً) إلى 5 (سمين جداً)</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- ملاحظات -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-sticky-note"></i>
                        ملاحظات إضافية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="4" 
                                  placeholder="أي ملاحظات إضافية حول البقرة..."></textarea>
                    </div>
                </div>
            </div>

            <!-- أزرار الحفظ -->
            <div class="card">
                <div class="card-body">
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('herd_management') }}" class="btn btn-outline-secondary me-md-2">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ البقرة
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- معلومات السلالة -->
<div class="row justify-content-center mt-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle"></i>
                    معلومات السلالات
                </h6>
            </div>
            <div class="card-body">
                <div id="breed-info" class="text-muted">
                    اختر سلالة لعرض معلوماتها
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تعيين تاريخ اليوم كتاريخ افتراضي
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('registration_date').value = today;
    
    // تعيين تاريخ ميلاد افتراضي (3 سنوات مضت)
    const birthDate = new Date();
    birthDate.setFullYear(birthDate.getFullYear() - 3);
    document.getElementById('birth_date').value = birthDate.toISOString().split('T')[0];
});

// تحديث معلومات السلالة عند التغيير
document.getElementById('breed').addEventListener('change', function() {
    const selectedOption = this.options[this.selectedIndex];
    const breedInfo = document.getElementById('breed-info');
    const weightInput = document.getElementById('weight');
    
    if (selectedOption.value) {
        const avgWeight = selectedOption.dataset.weight;
        const milkPotential = selectedOption.dataset.milk;
        
        breedInfo.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <strong>متوسط الوزن:</strong> ${avgWeight} كغ
                </div>
                <div class="col-md-6">
                    <strong>إمكانية الإنتاج:</strong> ${milkPotential} لتر/يوم
                </div>
            </div>
        `;
        
        // تحديث الوزن الافتراضي
        weightInput.value = avgWeight;
    } else {
        breedInfo.innerHTML = 'اختر سلالة لعرض معلوماتها';
        weightInput.value = 600;
    }
});

// تحديث استهلاك العلف حسب الوزن
document.getElementById('weight').addEventListener('input', function() {
    const weight = parseFloat(this.value);
    const feedIntakeInput = document.getElementById('daily_feed_intake');
    
    if (weight && weight > 0) {
        // حساب استهلاك العلف (3.5% من وزن الجسم)
        const feedIntake = (weight * 0.035).toFixed(1);
        feedIntakeInput.value = feedIntake;
    }
});

// التحقق من صحة النموذج
(function() {
    'use strict';
    window.addEventListener('load', function() {
        const forms = document.getElementsByClassName('needs-validation');
        const validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// تحديث اسم البقرة تلقائياً
document.getElementById('cow_id').addEventListener('input', function() {
    const nameInput = document.getElementById('name');
    if (!nameInput.value) {
        nameInput.placeholder = `بقرة رقم ${this.value}`;
    }
});

// تحديث تاريخ آخر ولادة حسب الحالة الصحية
document.getElementById('health_status').addEventListener('change', function() {
    const calvingDateInput = document.getElementById('last_calving_date');
    const lactationSelect = document.getElementById('lactation_number');
    
    if (this.value === 'جافة') {
        // إذا كانت جافة، قد تكون لم تلد بعد
        calvingDateInput.value = '';
        lactationSelect.value = '1';
    } else if (this.value === 'حامل') {
        // إذا كانت حامل، يجب أن تكون ولدت من قبل
        if (!calvingDateInput.value) {
            const lastCalving = new Date();
            lastCalving.setMonth(lastCalving.getMonth() - 8); // 8 أشهر مضت
            calvingDateInput.value = lastCalving.toISOString().split('T')[0];
        }
    }
});
</script>
{% endblock %}
