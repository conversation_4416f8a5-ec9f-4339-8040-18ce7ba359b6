<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام إدارة مزرعة الأبقار الحلوب{% endblock %}</title>
    
    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom CSS -->
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }
        
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #2E86AB 0%, #A23B72 100%);
            color: white;
            padding: 0;
        }
        
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 1rem 1.5rem;
            border-radius: 0;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            border-right: 4px solid #F18F01;
        }
        
        .sidebar .nav-link i {
            margin-left: 0.5rem;
            width: 20px;
        }
        
        .main-content {
            padding: 2rem;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease;
        }
        
        .card:hover {
            transform: translateY(-2px);
        }
        
        .card-header {
            background: linear-gradient(135deg, #2E86AB 0%, #A23B72 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            font-weight: 600;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #2E86AB 0%, #A23B72 100%);
            border: none;
            border-radius: 10px;
            padding: 0.5rem 1.5rem;
            font-weight: 600;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #1E5F7A 0%, #8B2A5B 100%);
            transform: translateY(-1px);
        }
        
        .btn-success {
            background-color: #F18F01;
            border-color: #F18F01;
            border-radius: 10px;
        }
        
        .btn-success:hover {
            background-color: #D17A01;
            border-color: #D17A01;
        }
        
        .btn-danger {
            background-color: #C73E1D;
            border-color: #C73E1D;
            border-radius: 10px;
        }
        
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        
        .table thead th {
            background: linear-gradient(135deg, #2E86AB 0%, #A23B72 100%);
            color: white;
            border: none;
            font-weight: 600;
        }
        
        .table tbody tr:hover {
            background-color: rgba(46, 134, 171, 0.1);
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            transition: border-color 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #2E86AB;
            box-shadow: 0 0 0 0.2rem rgba(46, 134, 171, 0.25);
        }
        
        .stats-card {
            background: linear-gradient(135deg, #2E86AB 0%, #A23B72 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
        }
        
        .stats-card h3 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .stats-card p {
            margin: 0;
            opacity: 0.9;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 2rem;
        }
        
        .spinner-border {
            color: #2E86AB;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                min-height: auto;
            }
            
            .main-content {
                padding: 1rem;
            }
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #2E86AB 0%, #A23B72 100%);">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-cow"></i>
                نظام إدارة مزرعة الأبقار
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">
                            <i class="fas fa-home"></i> الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('herd_management') }}">
                            <i class="fas fa-cow"></i> إدارة القطيع
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('feed_management') }}">
                            <i class="fas fa-seedling"></i> إدارة الأعلاف
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('milk_tracking') }}">
                            <i class="fas fa-tint"></i> تتبع الحليب
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('reports_dashboard') }}">
                            <i class="fas fa-chart-bar"></i> التقارير
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> المدير
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog"></i> الإعدادات</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-download"></i> نسخة احتياطية</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'index' %}active{% endif %}" href="{{ url_for('index') }}">
                                <i class="fas fa-tachometer-alt"></i>
                                لوحة المعلومات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint and 'herd' in request.endpoint %}active{% endif %}" href="{{ url_for('herd_management') }}">
                                <i class="fas fa-cow"></i>
                                إدارة القطيع
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint and 'feed' in request.endpoint %}active{% endif %}" href="{{ url_for('feed_management') }}">
                                <i class="fas fa-seedling"></i>
                                إدارة الأعلاف
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint and 'milk' in request.endpoint %}active{% endif %}" href="{{ url_for('milk_tracking') }}">
                                <i class="fas fa-tint"></i>
                                تتبع الحليب
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint and 'reports' in request.endpoint %}active{% endif %}" href="{{ url_for('reports_dashboard') }}">
                                <i class="fas fa-chart-bar"></i>
                                التقارير والإحصائيات
                            </a>
                        </li>
                        
                        <hr class="my-3" style="border-color: rgba(255,255,255,0.3);">
                        
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="generateBackup()">
                                <i class="fas fa-download"></i>
                                نسخة احتياطية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" data-bs-toggle="modal" data-bs-target="#helpModal">
                                <i class="fas fa-question-circle"></i>
                                المساعدة
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' if category == 'success' else 'info-circle' }}"></i>
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <!-- Page Content -->
                {% block content %}{% endblock %}
            </div>
        </div>
    </div>

    <!-- Help Modal -->
    <div class="modal fade" id="helpModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">دليل المستخدم</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <h6>إدارة القطيع:</h6>
                    <ul>
                        <li>إضافة وتعديل بيانات الأبقار</li>
                        <li>تتبع الحالة الصحية والإنتاجية</li>
                        <li>فلترة والبحث في القطيع</li>
                    </ul>
                    
                    <h6>إدارة الأعلاف:</h6>
                    <ul>
                        <li>إنشاء خلطات علف مخصصة</li>
                        <li>تحليل القيمة الغذائية</li>
                        <li>حساب التكلفة والكفاءة</li>
                    </ul>
                    
                    <h6>تتبع الحليب:</h6>
                    <ul>
                        <li>تسجيل إنتاج الحليب اليومي</li>
                        <li>مراقبة الاتجاهات والأنماط</li>
                        <li>تحليل الأداء الإنتاجي</li>
                    </ul>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        // تحديث الوقت
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('ar-SA');
            const timeElement = document.getElementById('current-time');
            if (timeElement) {
                timeElement.textContent = timeString;
            }
        }
        
        // تحديث الوقت كل ثانية
        setInterval(updateTime, 1000);
        updateTime();
        
        // إنشاء نسخة احتياطية
        function generateBackup() {
            if (confirm('هل تريد إنشاء نسخة احتياطية من قاعدة البيانات؟')) {
                // هنا يمكن إضافة كود إنشاء النسخة الاحتياطية
                alert('تم إنشاء النسخة الاحتياطية بنجاح');
            }
        }
        
        // تأكيد الحذف
        function confirmDelete(message) {
            return confirm(message || 'هل أنت متأكد من الحذف؟');
        }
        
        // إظهار/إخفاء مؤشر التحميل
        function showLoading() {
            document.querySelectorAll('.loading').forEach(el => {
                el.style.display = 'block';
            });
        }
        
        function hideLoading() {
            document.querySelectorAll('.loading').forEach(el => {
                el.style.display = 'none';
            });
        }
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
