{% extends "base.html" %}

{% block title %}تتبع الحليب - نظام إدارة مزرعة الأبقار{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h2">
        <i class="fas fa-tint text-primary"></i>
        تتبع إنتاج الحليب
    </h1>
    <div class="d-flex gap-2">
        <input type="date" id="dateSelector" class="form-control" value="{{ target_date.strftime('%Y-%m-%d') }}"
               onchange="changeDate(this.value)">
        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addMilkModal">
            <i class="fas fa-plus"></i>
            تسجيل إنتاج
        </button>
    </div>
</div>

<!-- ملخص اليوم -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-primary">{{ daily_summary.get('total_cows', 0) }}</h3>
                <p class="mb-0">عدد الأبقار المنتجة</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-success">{{ "%.1f"|format(daily_summary.get('total_milk', 0)) }}</h3>
                <p class="mb-0">إجمالي الإنتاج (لتر)</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-info">{{ "%.1f"|format(daily_summary.get('average_milk', 0)) }}</h3>
                <p class="mb-0">متوسط الإنتاج (لتر)</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-warning">{{ "%.1f"|format(daily_summary.get('max_milk', 0)) }}</h3>
                <p class="mb-0">أعلى إنتاج (لتر)</p>
            </div>
        </div>
    </div>
</div>

<!-- جدول سجلات الحليب -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-list"></i>
            سجلات الحليب - {{ target_date.strftime('%Y-%m-%d') }}
        </h5>
        <div class="d-flex gap-2">
            <button type="button" class="btn btn-outline-primary btn-sm" onclick="exportToExcel()">
                <i class="fas fa-file-excel"></i>
                تصدير Excel
            </button>
            <button type="button" class="btn btn-outline-info btn-sm" onclick="generateReport()">
                <i class="fas fa-file-pdf"></i>
                تقرير PDF
            </button>
        </div>
    </div>
    <div class="card-body">
        {% if milk_records %}
        <div class="table-responsive">
            <table class="table table-hover" id="milkTable">
                <thead>
                    <tr>
                        <th>رقم البقرة</th>
                        <th>اسم البقرة</th>
                        <th>الحليب الصباحي (لتر)</th>
                        <th>الحليب المسائي (لتر)</th>
                        <th>الإجمالي اليومي (لتر)</th>
                        <th>أيام الحليب</th>
                        <th>ملاحظات</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for record in milk_records %}
                    <tr>
                        <td><strong>{{ record.cow_id }}</strong></td>
                        <td>{{ record.cow_name }}</td>
                        <td>
                            <span class="badge bg-info">{{ "%.1f"|format(record.morning_milk) }}</span>
                        </td>
                        <td>
                            <span class="badge bg-warning">{{ "%.1f"|format(record.evening_milk) }}</span>
                        </td>
                        <td>
                            <span class="badge bg-success fs-6">{{ "%.1f"|format(record.daily_total) }}</span>
                        </td>
                        <td>
                            <span class="badge bg-secondary">{{ record.days_in_milk }}</span>
                        </td>
                        <td>
                            {% if record.notes %}
                                <i class="fas fa-sticky-note text-info" title="{{ record.notes }}"></i>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            <button type="button" class="btn btn-outline-primary btn-sm" 
                                    onclick="editMilkRecord('{{ record.cow_id }}', {{ record.morning_milk }}, {{ record.evening_milk }}, '{{ record.notes }}')">
                                <i class="fas fa-edit"></i>
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
                <tfoot>
                    <tr class="table-info">
                        <th colspan="2">الإجمالي</th>
                        <th>{{ "%.1f"|format(milk_records|sum(attribute='morning_milk')) }}</th>
                        <th>{{ "%.1f"|format(milk_records|sum(attribute='evening_milk')) }}</th>
                        <th>{{ "%.1f"|format(milk_records|sum(attribute='daily_total')) }}</th>
                        <th colspan="3">-</th>
                    </tr>
                </tfoot>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-tint fa-5x text-muted mb-3"></i>
            <h4 class="text-muted">لا توجد سجلات حليب</h4>
            <p class="text-muted">لم يتم تسجيل إنتاج الحليب لهذا التاريخ</p>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addMilkModal">
                <i class="fas fa-plus"></i>
                تسجيل إنتاج الحليب
            </button>
        </div>
        {% endif %}
    </div>
</div>

<!-- الأبقار التي لم تسجل إنتاجها -->
{% set recorded_cows = milk_records|map(attribute='cow_id')|list %}
{% set unrecorded_cows = active_cows|rejectattr('cow_id', 'in', recorded_cows)|list %}

{% if unrecorded_cows %}
<div class="card mt-4">
    <div class="card-header">
        <h5 class="mb-0 text-warning">
            <i class="fas fa-exclamation-triangle"></i>
            أبقار لم يتم تسجيل إنتاجها ({{ unrecorded_cows|length }})
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            {% for cow in unrecorded_cows %}
            <div class="col-md-4 mb-2">
                <div class="d-flex justify-content-between align-items-center p-2 bg-light rounded">
                    <span>{{ cow.cow_id }} - {{ cow.name }}</span>
                    <button type="button" class="btn btn-outline-success btn-sm" 
                            onclick="quickAddMilk('{{ cow.cow_id }}', '{{ cow.name }}')">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}

<!-- نافذة إضافة/تعديل سجل الحليب -->
<div class="modal fade" id="addMilkModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تسجيل إنتاج الحليب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('add_milk_record') }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="cow_id" class="form-label">البقرة <span class="text-danger">*</span></label>
                        <select class="form-select" id="cow_id" name="cow_id" required>
                            <option value="">اختر البقرة</option>
                            {% for cow in active_cows %}
                            <option value="{{ cow.cow_id }}">{{ cow.cow_id }} - {{ cow.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="record_date" class="form-label">التاريخ <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="record_date" name="record_date" 
                               value="{{ target_date.strftime('%Y-%m-%d') }}" required>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="morning_milk" class="form-label">الحليب الصباحي (لتر)</label>
                            <input type="number" class="form-control" id="morning_milk" name="morning_milk" 
                                   min="0" max="50" step="0.1" value="0">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="evening_milk" class="form-label">الحليب المسائي (لتر)</label>
                            <input type="number" class="form-control" id="evening_milk" name="evening_milk" 
                                   min="0" max="50" step="0.1" value="0">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="total_display" class="form-label">الإجمالي اليومي</label>
                        <input type="text" class="form-control" id="total_display" readonly>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                  placeholder="أي ملاحظات حول إنتاج اليوم..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ السجل</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- رسم بياني للإنتاج -->
{% if milk_records %}
<div class="card mt-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-chart-bar"></i>
            رسم بياني للإنتاج
        </h5>
    </div>
    <div class="card-body">
        <canvas id="milkChart" height="100"></canvas>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
// تغيير التاريخ
function changeDate(newDate) {
    window.location.href = `{{ url_for('milk_tracking') }}?date=${newDate}`;
}

// حساب الإجمالي تلقائياً
document.addEventListener('DOMContentLoaded', function() {
    const morningInput = document.getElementById('morning_milk');
    const eveningInput = document.getElementById('evening_milk');
    const totalDisplay = document.getElementById('total_display');
    
    function updateTotal() {
        const morning = parseFloat(morningInput.value) || 0;
        const evening = parseFloat(eveningInput.value) || 0;
        const total = morning + evening;
        totalDisplay.value = total.toFixed(1) + ' لتر';
    }
    
    morningInput.addEventListener('input', updateTotal);
    eveningInput.addEventListener('input', updateTotal);
    updateTotal();
});

// إضافة سريعة للحليب
function quickAddMilk(cowId, cowName) {
    const modal = new bootstrap.Modal(document.getElementById('addMilkModal'));
    document.getElementById('cow_id').value = cowId;
    modal.show();
}

// تعديل سجل الحليب
function editMilkRecord(cowId, morningMilk, eveningMilk, notes) {
    const modal = new bootstrap.Modal(document.getElementById('addMilkModal'));
    document.getElementById('cow_id').value = cowId;
    document.getElementById('morning_milk').value = morningMilk;
    document.getElementById('evening_milk').value = eveningMilk;
    document.getElementById('notes').value = notes;
    
    // تحديث العنوان
    document.querySelector('#addMilkModal .modal-title').textContent = 'تعديل سجل الحليب';
    
    modal.show();
}

// تصدير إلى Excel
function exportToExcel() {
    const table = document.getElementById('milkTable');
    const wb = XLSX.utils.table_to_book(table);
    const date = document.getElementById('dateSelector').value;
    XLSX.writeFile(wb, `milk_records_${date}.xlsx`);
}

// إنشاء تقرير PDF
function generateReport() {
    const date = document.getElementById('dateSelector').value;
    window.open(`/api/generate_report/daily?date=${date}`, '_blank');
}

// رسم بياني للإنتاج
{% if milk_records %}
const ctx = document.getElementById('milkChart').getContext('2d');
const milkData = {
    labels: [{% for record in milk_records %}'{{ record.cow_id }}'{% if not loop.last %},{% endif %}{% endfor %}],
    datasets: [{
        label: 'الحليب الصباحي',
        data: [{% for record in milk_records %}{{ record.morning_milk }}{% if not loop.last %},{% endif %}{% endfor %}],
        backgroundColor: 'rgba(54, 162, 235, 0.8)',
        borderColor: 'rgba(54, 162, 235, 1)',
        borderWidth: 1
    }, {
        label: 'الحليب المسائي',
        data: [{% for record in milk_records %}{{ record.evening_milk }}{% if not loop.last %},{% endif %}{% endfor %}],
        backgroundColor: 'rgba(255, 206, 86, 0.8)',
        borderColor: 'rgba(255, 206, 86, 1)',
        borderWidth: 1
    }]
};

const milkChart = new Chart(ctx, {
    type: 'bar',
    data: milkData,
    options: {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: 'إنتاج الحليب حسب البقرة - {{ target_date.strftime("%Y-%m-%d") }}'
            },
            legend: {
                position: 'top'
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                title: {
                    display: true,
                    text: 'الكمية (لتر)'
                }
            },
            x: {
                title: {
                    display: true,
                    text: 'رقم البقرة'
                }
            }
        }
    }
});
{% endif %}

// إعادة تعيين النموذج عند إغلاق النافذة
document.getElementById('addMilkModal').addEventListener('hidden.bs.modal', function() {
    document.querySelector('#addMilkModal form').reset();
    document.querySelector('#addMilkModal .modal-title').textContent = 'تسجيل إنتاج الحليب';
    document.getElementById('record_date').value = '{{ target_date.strftime("%Y-%m-%d") }}';
});
</script>

<!-- مكتبة XLSX للتصدير -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
{% endblock %}
