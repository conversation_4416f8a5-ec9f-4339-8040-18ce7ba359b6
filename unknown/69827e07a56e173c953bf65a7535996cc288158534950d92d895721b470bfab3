{% extends "base.html" %}

{% block title %}تعديل خلطة العلف {{ feed_mix.name }} - نظام إدارة مزرعة الأبقار{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h2">
        <i class="fas fa-edit text-primary"></i>
        تعديل خلطة العلف: {{ feed_mix.name }}
    </h1>
    <a href="{{ url_for('feed_management') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-right"></i>
        العودة للقائمة
    </a>
</div>

<div class="row justify-content-center">
    <div class="col-lg-10">
        <form method="POST" class="needs-validation" novalidate>
            <!-- المعلومات الأساسية -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle"></i>
                        المعلومات الأساسية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="mix_id" class="form-label">رقم الخلطة</label>
                            <input type="text" class="form-control" id="mix_id" name="mix_id" 
                                   value="{{ feed_mix.mix_id }}" readonly>
                            <div class="form-text">لا يمكن تغيير رقم الخلطة</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">اسم الخلطة <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="{{ feed_mix.name }}" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">وصف الخلطة</label>
                        <textarea class="form-control" id="description" name="description" rows="3">{{ feed_mix.description }}</textarea>
                    </div>
                </div>
            </div>

            <!-- مكونات الخلطة -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-seedling"></i>
                        مكونات الخلطة
                    </h5>
                    <div>
                        <button type="button" class="btn btn-outline-success btn-sm" onclick="addCustomIngredient()">
                            <i class="fas fa-plus"></i>
                            إضافة مكون مخصص
                        </button>
                        <button type="button" class="btn btn-outline-info btn-sm" onclick="togglePriceEdit()">
                            <i class="fas fa-edit"></i>
                            تعديل الأسعار
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="ingredientsContainer">
                        <!-- المكونات الموجودة في الخلطة -->
                        {% for ingredient_key, percentage in feed_mix.ingredients.items() %}
                        {% if ingredient_key in feed_ingredients %}
                        <div class="ingredient-item mb-3" data-ingredient="{{ ingredient_key }}">
                            <div class="card border-primary">
                                <div class="card-body">
                                    <div class="row align-items-center">
                                        <div class="col-md-3">
                                            <h6 class="mb-1">{{ feed_ingredients[ingredient_key].name_ar }}</h6>
                                            <small class="text-muted">{{ feed_ingredients[ingredient_key].name_en }}</small>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label small">النسبة المئوية</label>
                                            <div class="input-group input-group-sm">
                                                <input type="number" class="form-control ingredient-percentage"
                                                       name="ingredient_{{ ingredient_key }}"
                                                       min="0" max="100" step="0.1"
                                                       value="{{ percentage }}">
                                                <span class="input-group-text">%</span>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label small">السعر (د.أ/كغ)</label>
                                            <input type="number" class="form-control form-control-sm ingredient-price"
                                                   step="0.01" min="0"
                                                   value="{{ feed_ingredients[ingredient_key].price_per_kg }}"
                                                   data-original="{{ feed_ingredients[ingredient_key].price_per_kg }}"
                                                   readonly>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="small text-muted">
                                                <div>بروتين: {{ feed_ingredients[ingredient_key].crude_protein }}%</div>
                                                <div>طاقة: {{ feed_ingredients[ingredient_key].energy_mcal }} ميجا كالوري/كغ</div>
                                            </div>
                                        </div>
                                        <div class="col-md-2 text-end">
                                            <button type="button" class="btn btn-outline-danger btn-sm"
                                                    onclick="removeIngredient('{{ ingredient_key }}')">
                                                <i class="fas fa-trash"></i>
                                                حذف
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        {% endfor %}

                        <!-- المكونات المتاحة للإضافة -->
                        <div class="mt-4">
                            <h6 class="text-muted">المكونات المتاحة للإضافة:</h6>
                            <div class="row" id="availableIngredients">
                                {% for ingredient_key, ingredient_data in feed_ingredients.items() %}
                                {% if ingredient_key not in feed_mix.ingredients %}
                                <div class="col-md-6 col-lg-4 mb-2" data-ingredient="{{ ingredient_key }}">
                                    <button type="button" class="btn btn-outline-secondary btn-sm w-100"
                                            onclick="addIngredient('{{ ingredient_key }}')">
                                        <i class="fas fa-plus"></i>
                                        {{ ingredient_data.name_ar }}
                                    </button>
                                </div>
                                {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info mt-3">
                        <i class="fas fa-info-circle"></i>
                        <strong>ملاحظة:</strong> مجموع النسب المئوية يجب أن يكون 100%. سيتم تطبيع النسب تلقائياً عند الحفظ.
                    </div>
                </div>
            </div>

            <!-- ملخص الخلطة -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calculator"></i>
                        ملخص الخلطة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3 mb-3">
                            <h5 id="totalPercentage" class="text-primary">0%</h5>
                            <small class="text-muted">إجمالي النسب</small>
                        </div>
                        <div class="col-md-3 mb-3">
                            <h5 id="estimatedProtein" class="text-success">0%</h5>
                            <small class="text-muted">البروتين المتوقع</small>
                        </div>
                        <div class="col-md-3 mb-3">
                            <h5 id="estimatedEnergy" class="text-info">0</h5>
                            <small class="text-muted">الطاقة المتوقعة (ميجا كالوري/كغ)</small>
                        </div>
                        <div class="col-md-3 mb-3">
                            <h5 id="estimatedCost" class="text-warning">0</h5>
                            <small class="text-muted">التكلفة المتوقعة (د.أ/كغ)</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات الخلطة الحالية -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line"></i>
                        معلومات الخلطة الحالية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <h5 class="text-primary">{{ "%.3f"|format(feed_mix.cost_per_kg) }}</h5>
                            <small class="text-muted">التكلفة الحالية (د.أ/كغ)</small>
                        </div>
                        <div class="col-md-3">
                            <h5 class="text-success">{{ "%.2f"|format(feed_mix.cost_per_ton) }}</h5>
                            <small class="text-muted">التكلفة (د.أ/طن)</small>
                        </div>
                        <div class="col-md-3">
                            <h5 class="text-info">{{ "%.0f"|format(feed_mix.quality_score) }}</h5>
                            <small class="text-muted">نقاط الجودة (%)</small>
                        </div>
                        <div class="col-md-3">
                            <h5 class="text-warning">{{ feed_mix.creation_date.strftime('%Y-%m-%d') }}</h5>
                            <small class="text-muted">تاريخ الإنشاء</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أزرار الحفظ -->
            <div class="card">
                <div class="card-body">
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('feed_management') }}" class="btn btn-outline-secondary me-md-2">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ التغييرات
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}

<!-- نافذة إضافة مكون مخصص -->
<div class="modal fade" id="customIngredientModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة مكون مخصص</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="customIngredientForm">
                    <div class="mb-3">
                        <label class="form-label">اسم المكون (عربي)</label>
                        <input type="text" class="form-control" id="customNameAr" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">اسم المكون (إنجليزي)</label>
                        <input type="text" class="form-control" id="customNameEn" required>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">البروتين الخام (%)</label>
                            <input type="number" class="form-control" id="customProtein" step="0.1" min="0" max="100" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الطاقة (ميجا كالوري/كغ)</label>
                            <input type="number" class="form-control" id="customEnergy" step="0.01" min="0" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">السعر (د.أ/كغ)</label>
                            <input type="number" class="form-control" id="customPrice" step="0.01" min="0" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">النسبة المئوية (%)</label>
                            <input type="number" class="form-control" id="customPercentage" step="0.1" min="0" max="100" required>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveCustomIngredient()">إضافة</button>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
// بيانات المكونات
const ingredients = {{ feed_ingredients|tojson }};
let priceEditMode = false;
let customIngredientCounter = 0;

// تحديث الملخص عند تغيير النسب
function updateSummary() {
    let totalPercentage = 0;
    let totalProtein = 0;
    let totalEnergy = 0;
    let totalCost = 0;

    // حساب من المكونات الموجودة في الخلطة
    document.querySelectorAll('.ingredient-item').forEach(item => {
        const percentageInput = item.querySelector('.ingredient-percentage');
        const priceInput = item.querySelector('.ingredient-price');
        const ingredientKey = item.dataset.ingredient;

        const percentage = parseFloat(percentageInput.value) || 0;
        const price = parseFloat(priceInput.value) || 0;

        totalPercentage += percentage;
        totalCost += (price * percentage / 100);

        if (ingredients[ingredientKey]) {
            const ingredient = ingredients[ingredientKey];
            totalProtein += (ingredient.crude_protein * percentage / 100);
            totalEnergy += (ingredient.energy_mcal * percentage / 100);
        } else {
            // مكون مخصص
            const proteinElement = item.querySelector('[data-protein]');
            const energyElement = item.querySelector('[data-energy]');
            if (proteinElement && energyElement) {
                totalProtein += (parseFloat(proteinElement.dataset.protein) * percentage / 100);
                totalEnergy += (parseFloat(energyElement.dataset.energy) * percentage / 100);
            }
        }
    });

    document.getElementById('totalPercentage').textContent = totalPercentage.toFixed(1) + '%';
    document.getElementById('estimatedProtein').textContent = totalProtein.toFixed(1) + '%';
    document.getElementById('estimatedEnergy').textContent = totalEnergy.toFixed(2);
    document.getElementById('estimatedCost').textContent = totalCost.toFixed(3);

    // تغيير لون إجمالي النسب
    const totalElement = document.getElementById('totalPercentage');
    if (Math.abs(totalPercentage - 100) < 0.1) {
        totalElement.className = 'text-success';
    } else if (totalPercentage > 100) {
        totalElement.className = 'text-danger';
    } else {
        totalElement.className = 'text-warning';
    }
}

// إضافة مكون موجود
function addIngredient(ingredientKey) {
    const ingredient = ingredients[ingredientKey];
    if (!ingredient) return;

    const container = document.getElementById('ingredientsContainer');
    const ingredientHtml = `
        <div class="ingredient-item mb-3" data-ingredient="${ingredientKey}">
            <div class="card border-primary">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-3">
                            <h6 class="mb-1">${ingredient.name_ar}</h6>
                            <small class="text-muted">${ingredient.name_en}</small>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label small">النسبة المئوية</label>
                            <div class="input-group input-group-sm">
                                <input type="number" class="form-control ingredient-percentage"
                                       name="ingredient_${ingredientKey}"
                                       min="0" max="100" step="0.1" value="0">
                                <span class="input-group-text">%</span>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label small">السعر (د.أ/كغ)</label>
                            <input type="number" class="form-control form-control-sm ingredient-price"
                                   step="0.01" min="0"
                                   value="${ingredient.price_per_kg}"
                                   data-original="${ingredient.price_per_kg}"
                                   ${priceEditMode ? '' : 'readonly'}>
                        </div>
                        <div class="col-md-3">
                            <div class="small text-muted">
                                <div>بروتين: ${ingredient.crude_protein}%</div>
                                <div>طاقة: ${ingredient.energy_mcal} ميجا كالوري/كغ</div>
                            </div>
                        </div>
                        <div class="col-md-2 text-end">
                            <button type="button" class="btn btn-outline-danger btn-sm"
                                    onclick="removeIngredient('${ingredientKey}')">
                                <i class="fas fa-trash"></i>
                                حذف
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إضافة المكون قبل المكونات المتاحة
    const availableSection = container.querySelector('.mt-4');
    availableSection.insertAdjacentHTML('beforebegin', ingredientHtml);

    // إخفاء الزر من المكونات المتاحة
    const availableButton = document.querySelector(`[data-ingredient="${ingredientKey}"]`);
    if (availableButton && availableButton.closest('.col-md-6')) {
        availableButton.closest('.col-md-6').style.display = 'none';
    }

    // ربط الأحداث
    bindIngredientEvents();
    updateSummary();
}

// حذف مكون
function removeIngredient(ingredientKey) {
    if (confirm('هل أنت متأكد من حذف هذا المكون؟')) {
        const ingredientItem = document.querySelector(`[data-ingredient="${ingredientKey}"]`);
        if (ingredientItem && ingredientItem.classList.contains('ingredient-item')) {
            ingredientItem.remove();

            // إظهار الزر في المكونات المتاحة
            const availableButton = document.querySelector(`#availableIngredients [data-ingredient="${ingredientKey}"]`);
            if (availableButton) {
                availableButton.style.display = 'block';
            }

            updateSummary();
        }
    }
}

// تبديل وضع تعديل الأسعار
function togglePriceEdit() {
    const button = event.target.closest('button');

    if (!priceEditMode) {
        // تفعيل وضع التعديل
        priceEditMode = true;
        const priceInputs = document.querySelectorAll('.ingredient-price');

        priceInputs.forEach(input => {
            input.readOnly = false;
            input.classList.add('border-warning');
        });

        button.innerHTML = '<i class="fas fa-save"></i> حفظ الأسعار';
        button.className = 'btn btn-warning btn-sm';
    } else {
        // حفظ الأسعار
        savePriceChanges(button);
    }
}

// حفظ تغييرات الأسعار
function savePriceChanges(button) {
    const priceInputs = document.querySelectorAll('.ingredient-price');
    const updatedPrices = {};
    let hasChanges = false;

    priceInputs.forEach(input => {
        const ingredientKey = input.closest('.ingredient-item').dataset.ingredient;
        const newPrice = parseFloat(input.value);
        const originalPrice = parseFloat(input.dataset.original);

        if (ingredientKey in ingredients && Math.abs(newPrice - originalPrice) > 0.001) {
            updatedPrices[ingredientKey] = newPrice;
            hasChanges = true;
        }
    });

    if (hasChanges) {
        // إرسال التحديثات للخادم
        fetch('/api/update_ingredient_prices', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                prices: updatedPrices
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // تحديث الأسعار الأصلية
                priceInputs.forEach(input => {
                    input.dataset.original = input.value;
                    input.classList.remove('border-warning');
                });

                // تحديث بيانات المكونات في JavaScript
                Object.keys(updatedPrices).forEach(key => {
                    if (ingredients[key]) {
                        ingredients[key].price_per_kg = updatedPrices[key];
                    }
                });

                alert('تم حفظ الأسعار بنجاح وإعادة حساب التكاليف');
                updateSummary();
            } else {
                alert('خطأ في حفظ الأسعار: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('خطأ في الاتصال بالخادم');
        });
    }

    // إنهاء وضع التعديل
    priceEditMode = false;
    priceInputs.forEach(input => {
        input.readOnly = true;
        input.classList.remove('border-warning');
    });

    button.innerHTML = '<i class="fas fa-edit"></i> تعديل الأسعار';
    button.className = 'btn btn-outline-info btn-sm';
}

// إضافة مكون مخصص
function addCustomIngredient() {
    const modal = new bootstrap.Modal(document.getElementById('customIngredientModal'));
    modal.show();
}

// حفظ المكون المخصص
function saveCustomIngredient() {
    const form = document.getElementById('customIngredientForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const nameAr = document.getElementById('customNameAr').value;
    const nameEn = document.getElementById('customNameEn').value;
    const protein = parseFloat(document.getElementById('customProtein').value);
    const energy = parseFloat(document.getElementById('customEnergy').value);
    const price = parseFloat(document.getElementById('customPrice').value);
    const percentage = parseFloat(document.getElementById('customPercentage').value);

    customIngredientCounter++;
    const customKey = `custom_${customIngredientCounter}`;

    const container = document.getElementById('ingredientsContainer');
    const ingredientHtml = `
        <div class="ingredient-item mb-3" data-ingredient="${customKey}">
            <div class="card border-success">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-3">
                            <h6 class="mb-1">${nameAr} <span class="badge bg-success">مخصص</span></h6>
                            <small class="text-muted">${nameEn}</small>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label small">النسبة المئوية</label>
                            <div class="input-group input-group-sm">
                                <input type="number" class="form-control ingredient-percentage"
                                       name="ingredient_${customKey}"
                                       min="0" max="100" step="0.1" value="${percentage}">
                                <span class="input-group-text">%</span>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label small">السعر (د.أ/كغ)</label>
                            <input type="number" class="form-control form-control-sm ingredient-price"
                                   step="0.01" min="0" value="${price}">
                        </div>
                        <div class="col-md-3">
                            <div class="small text-muted">
                                <div data-protein="${protein}">بروتين: ${protein}%</div>
                                <div data-energy="${energy}">طاقة: ${energy} ميجا كالوري/كغ</div>
                            </div>
                        </div>
                        <div class="col-md-2 text-end">
                            <button type="button" class="btn btn-outline-danger btn-sm"
                                    onclick="removeIngredient('${customKey}')">
                                <i class="fas fa-trash"></i>
                                حذف
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إضافة المكون قبل المكونات المتاحة
    const availableSection = container.querySelector('.mt-4');
    availableSection.insertAdjacentHTML('beforebegin', ingredientHtml);

    // إغلاق النافذة وتنظيف النموذج
    bootstrap.Modal.getInstance(document.getElementById('customIngredientModal')).hide();
    form.reset();

    // ربط الأحداث وتحديث الملخص
    bindIngredientEvents();
    updateSummary();
}

// ربط أحداث المكونات
function bindIngredientEvents() {
    document.querySelectorAll('.ingredient-percentage, .ingredient-price').forEach(input => {
        input.removeEventListener('input', updateSummary);
        input.addEventListener('input', updateSummary);
    });
}

// ربط الأحداث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    bindIngredientEvents();
    updateSummary();
});

// التحقق من صحة النموذج
(function() {
    'use strict';
    window.addEventListener('load', function() {
        const forms = document.getElementsByClassName('needs-validation');
        const validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                const totalPercentage = parseFloat(document.getElementById('totalPercentage').textContent);

                if (form.checkValidity() === false || totalPercentage === 0) {
                    event.preventDefault();
                    event.stopPropagation();

                    if (totalPercentage === 0) {
                        alert('يرجى إدخال نسب المكونات');
                    }
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
{% endblock %}
