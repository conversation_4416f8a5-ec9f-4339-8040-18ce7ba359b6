{% extends "base.html" %}

{% block title %}لوحة المعلومات - نظام إدارة مزرعة الأبقار{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h2">
        <i class="fas fa-tachometer-alt text-primary"></i>
        لوحة المعلومات
    </h1>
    <div class="text-muted">
        <i class="fas fa-calendar-alt"></i>
        {{ current_date.strftime('%Y-%m-%d') }}
        <span id="current-time" class="ms-2"></span>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="stats-card">
            <h3>{{ herd_stats.get('total_cows', 0) }}</h3>
            <p><i class="fas fa-cow"></i> إجمالي الأبقار</p>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #F18F01 0%, #C73E1D 100%);">
            <h3>{{ herd_stats.get('active_cows', 0) }}</h3>
            <p><i class="fas fa-heart"></i> الأبقار النشطة</p>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
            <h3>{{ "%.1f"|format(today_summary.get('total_milk', 0)) }}</h3>
            <p><i class="fas fa-tint"></i> إنتاج اليوم (لتر)</p>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);">
            <h3>{{ "%.1f"|format(herd_stats.get('herd_average_milk', 0)) }}</h3>
            <p><i class="fas fa-chart-line"></i> متوسط الإنتاج</p>
        </div>
    </div>
</div>

<div class="row">
    <!-- أعلى منتجات الحليب -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-trophy"></i>
                    أعلى منتجات الحليب
                </h5>
            </div>
            <div class="card-body">
                {% if top_producers %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>الترتيب</th>
                                    <th>رقم البقرة</th>
                                    <th>الاسم</th>
                                    <th>متوسط الإنتاج</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for cow in top_producers %}
                                <tr>
                                    <td>
                                        {% if loop.index == 1 %}
                                            <i class="fas fa-medal text-warning"></i>
                                        {% elif loop.index == 2 %}
                                            <i class="fas fa-medal text-secondary"></i>
                                        {% elif loop.index == 3 %}
                                            <i class="fas fa-medal text-danger"></i>
                                        {% else %}
                                            {{ loop.index }}
                                        {% endif %}
                                    </td>
                                    <td>{{ cow.cow_id }}</td>
                                    <td>{{ cow.name }}</td>
                                    <td>
                                        <span class="badge bg-success">
                                            {{ "%.1f"|format(cow.average_daily_milk) }} لتر
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-cow fa-3x mb-3"></i>
                        <p>لا توجد بيانات إنتاج متاحة</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- ملخص إنتاج اليوم -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-calendar-day"></i>
                    ملخص إنتاج اليوم
                </h5>
            </div>
            <div class="card-body">
                {% if today_summary.get('total_cows', 0) > 0 %}
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <h4 class="text-primary">{{ today_summary.get('total_cows', 0) }}</h4>
                            <small class="text-muted">عدد الأبقار المنتجة</small>
                        </div>
                        <div class="col-6 mb-3">
                            <h4 class="text-success">{{ "%.1f"|format(today_summary.get('average_milk', 0)) }}</h4>
                            <small class="text-muted">متوسط الإنتاج (لتر)</small>
                        </div>
                        <div class="col-6 mb-3">
                            <h4 class="text-info">{{ "%.1f"|format(today_summary.get('max_milk', 0)) }}</h4>
                            <small class="text-muted">أعلى إنتاج (لتر)</small>
                        </div>
                        <div class="col-6 mb-3">
                            <h4 class="text-warning">{{ "%.1f"|format(today_summary.get('min_milk', 0)) }}</h4>
                            <small class="text-muted">أقل إنتاج (لتر)</small>
                        </div>
                    </div>
                    
                    <!-- رسم بياني سريع -->
                    <canvas id="todayProductionChart" height="200"></canvas>
                {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-tint fa-3x mb-3"></i>
                        <p>لم يتم تسجيل إنتاج اليوم بعد</p>
                        <a href="{{ url_for('milk_tracking') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> تسجيل الإنتاج
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- خلطات العلف النشطة -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-seedling"></i>
                    خلطات العلف النشطة
                </h5>
            </div>
            <div class="card-body">
                {% if active_mixes %}
                    {% for mix in active_mixes %}
                    <div class="d-flex justify-content-between align-items-center mb-3 p-3 bg-light rounded">
                        <div>
                            <h6 class="mb-1">{{ mix.name }}</h6>
                            <small class="text-muted">{{ mix.description or 'لا يوجد وصف' }}</small>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-primary">{{ "%.2f"|format(mix.cost_per_kg) }} ريال/كغ</span>
                            <br>
                            <small class="text-muted">جودة: {{ "%.0f"|format(mix.quality_score) }}%</small>
                        </div>
                    </div>
                    {% endfor %}
                    
                    <div class="text-center mt-3">
                        <a href="{{ url_for('feed_management') }}" class="btn btn-outline-primary">
                            عرض جميع الخلطات
                        </a>
                    </div>
                {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-seedling fa-3x mb-3"></i>
                        <p>لا توجد خلطات علف نشطة</p>
                        <a href="{{ url_for('add_feed_mix') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> إضافة خلطة
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- الأحداث الأخيرة -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bell"></i>
                    الأحداث الأخيرة
                </h5>
            </div>
            <div class="card-body">
                {% if recent_events %}
                    <div class="timeline">
                        {% for event in recent_events[:5] %}
                        <div class="d-flex mb-3">
                            <div class="flex-shrink-0">
                                {% if event.severity == 'warning' %}
                                    <i class="fas fa-exclamation-triangle text-warning"></i>
                                {% elif event.severity == 'error' %}
                                    <i class="fas fa-times-circle text-danger"></i>
                                {% else %}
                                    <i class="fas fa-info-circle text-info"></i>
                                {% endif %}
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">{{ event.description }}</h6>
                                <small class="text-muted">
                                    {% if event.cow_name %}
                                        البقرة: {{ event.cow_name }} |
                                    {% endif %}
                                    {{ event.event_date }}
                                </small>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    
                    {% if recent_events|length > 5 %}
                    <div class="text-center mt-3">
                        <a href="#" class="btn btn-outline-primary btn-sm">
                            عرض جميع الأحداث
                        </a>
                    </div>
                    {% endif %}
                {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-bell fa-3x mb-3"></i>
                        <p>لا توجد أحداث جديدة</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- إجراءات سريعة -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('add_cow') }}" class="btn btn-primary btn-lg w-100">
                            <i class="fas fa-plus-circle fa-2x d-block mb-2"></i>
                            إضافة بقرة جديدة
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('milk_tracking') }}" class="btn btn-success btn-lg w-100">
                            <i class="fas fa-tint fa-2x d-block mb-2"></i>
                            تسجيل إنتاج الحليب
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('add_feed_mix') }}" class="btn btn-warning btn-lg w-100">
                            <i class="fas fa-seedling fa-2x d-block mb-2"></i>
                            إنشاء خلطة علف
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('reports_dashboard') }}" class="btn btn-info btn-lg w-100">
                            <i class="fas fa-chart-bar fa-2x d-block mb-2"></i>
                            عرض التقارير
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// رسم بياني لإنتاج اليوم
{% if today_summary.get('total_cows', 0) > 0 %}
const ctx = document.getElementById('todayProductionChart').getContext('2d');
const chart = new Chart(ctx, {
    type: 'doughnut',
    data: {
        labels: ['متوسط الإنتاج', 'أعلى إنتاج', 'أقل إنتاج'],
        datasets: [{
            data: [
                {{ today_summary.get('average_milk', 0) }},
                {{ today_summary.get('max_milk', 0) }},
                {{ today_summary.get('min_milk', 0) }}
            ],
            backgroundColor: [
                '#2E86AB',
                '#F18F01',
                '#C73E1D'
            ],
            borderWidth: 0
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
{% endif %}

// تحديث البيانات كل دقيقة
setInterval(function() {
    location.reload();
}, 60000);
</script>
{% endblock %}
