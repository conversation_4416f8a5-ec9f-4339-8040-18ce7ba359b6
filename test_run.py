#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تشغيل سريع للنظام
Quick test run for the system
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """اختبار الاستيرادات"""
    print("🧪 اختبار الاستيرادات...")
    
    try:
        from config.nutrition_standards import FEED_INGREDIENTS, BREED_WEIGHTS
        print("✅ config.nutrition_standards")
    except Exception as e:
        print(f"❌ config.nutrition_standards: {e}")
        return False
    
    try:
        from models.cow import Cow
        print("✅ models.cow")
    except Exception as e:
        print(f"❌ models.cow: {e}")
        return False
    
    try:
        from models.feed_mix import FeedMix
        print("✅ models.feed_mix")
    except Exception as e:
        print(f"❌ models.feed_mix: {e}")
        return False
    
    try:
        from database.db_manager import DatabaseManager
        print("✅ database.db_manager")
    except Exception as e:
        print(f"❌ database.db_manager: {e}")
        return False
    
    try:
        from utils.nutrition_analyzer import NutritionAnalyzer
        print("✅ utils.nutrition_analyzer")
    except Exception as e:
        print(f"❌ utils.nutrition_analyzer: {e}")
        return False
    
    try:
        from utils.report_generator import ReportGenerator
        print("✅ utils.report_generator")
    except Exception as e:
        print(f"❌ utils.report_generator: {e}")
        return False
    
    return True

def test_database():
    """اختبار قاعدة البيانات"""
    print("\n🗄️  اختبار قاعدة البيانات...")
    
    try:
        from database.db_manager import DatabaseManager
        db_manager = DatabaseManager()
        print("✅ تم إنشاء قاعدة البيانات")
        
        # اختبار إحصائيات القطيع
        stats = db_manager.get_herd_statistics()
        print(f"✅ إحصائيات القطيع: {stats}")
        
        return True
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def test_flask_app():
    """اختبار تطبيق Flask"""
    print("\n🌐 اختبار تطبيق Flask...")
    
    try:
        from app import app
        print("✅ تم تحميل تطبيق Flask")
        
        # اختبار إعداد التطبيق
        with app.app_context():
            print("✅ سياق التطبيق يعمل")
        
        return True
    except Exception as e:
        print(f"❌ خطأ في تطبيق Flask: {e}")
        return False

def create_simple_demo():
    """إنشاء بيانات تجريبية بسيطة"""
    print("\n📊 إنشاء بيانات تجريبية بسيطة...")
    
    try:
        from database.db_manager import DatabaseManager
        from models.cow import Cow
        from models.feed_mix import FeedMix
        from datetime import date, timedelta
        
        db_manager = DatabaseManager()
        
        # إنشاء بقرة تجريبية
        cow = Cow(
            cow_id='TEST001',
            name='بقرة تجريبية',
            breed='holstein',
            birth_date=date.today() - timedelta(days=1095),  # 3 سنوات
            weight=650.0
        )
        
        cow.last_calving_date = date.today() - timedelta(days=60)
        cow.health_status = 'سليمة'
        
        # إضافة سجل حليب
        cow.add_milk_record(date.today(), 15.5, 12.3)
        
        # حفظ البقرة
        if db_manager.add_cow(cow):
            print("✅ تم إضافة بقرة تجريبية")
        else:
            print("⚠️  البقرة التجريبية موجودة مسبقاً")
        
        # إنشاء خلطة علف تجريبية
        feed_mix = FeedMix(
            mix_id='TEST_MIX',
            name='خلطة تجريبية',
            description='خلطة للاختبار'
        )
        
        feed_mix.add_ingredient('corn', 40.0)
        feed_mix.add_ingredient('soybean_meal', 20.0)
        feed_mix.add_ingredient('alfalfa_hay', 30.0)
        feed_mix.add_ingredient('limestone', 10.0)
        
        if db_manager.add_feed_mix(feed_mix):
            print("✅ تم إضافة خلطة علف تجريبية")
        else:
            print("⚠️  خلطة العلف التجريبية موجودة مسبقاً")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء البيانات التجريبية: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🧪 اختبار سريع لنظام إدارة مزرعة الأبقار الحلوب")
    print("=" * 60)
    
    # اختبار الاستيرادات
    if not test_imports():
        print("\n❌ فشل في اختبار الاستيرادات")
        return False
    
    # اختبار قاعدة البيانات
    if not test_database():
        print("\n❌ فشل في اختبار قاعدة البيانات")
        return False
    
    # اختبار تطبيق Flask
    if not test_flask_app():
        print("\n❌ فشل في اختبار تطبيق Flask")
        return False
    
    # إنشاء بيانات تجريبية
    if not create_simple_demo():
        print("\n❌ فشل في إنشاء البيانات التجريبية")
        return False
    
    print("\n" + "=" * 60)
    print("✅ جميع الاختبارات نجحت!")
    print("=" * 60)
    
    print("\n🚀 النظام جاهز للتشغيل!")
    print("شغل الأمر: python main.py")
    print("أو: python app.py")
    
    return True

if __name__ == '__main__':
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف الاختبار")
    except Exception as e:
        print(f"\n❌ خطأ عام: {e}")
        sys.exit(1)
    
    input("\nاضغط Enter للخروج...")
