@echo off
chcp 65001 > nul
title نظام إدارة مزرعة الأبقار الحلوب

echo.
echo ===============================================
echo 🐄 نظام إدارة مزرعة الأبقار الحلوب
echo    Dairy Farm Management System
echo ===============================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.8 أو أحدث من: https://python.org
    pause
    exit /b 1
)

echo ✅ تم العثور على Python

REM التحقق من وجود pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: pip غير متاح
    echo يرجى إعادة تثبيت Python مع pip
    pause
    exit /b 1
)

echo ✅ تم العثور على pip

REM التحقق من وجود البيئة الافتراضية
if not exist "venv" (
    echo 📦 إنشاء البيئة الافتراضية...
    python -m venv venv
    if errorlevel 1 (
        echo ❌ فشل في إنشاء البيئة الافتراضية
        pause
        exit /b 1
    )
    echo ✅ تم إنشاء البيئة الافتراضية
)

REM تفعيل البيئة الافتراضية
echo 🔄 تفعيل البيئة الافتراضية...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo ❌ فشل في تفعيل البيئة الافتراضية
    pause
    exit /b 1
)

echo ✅ تم تفعيل البيئة الافتراضية

REM تثبيت المتطلبات
echo 📥 تثبيت المتطلبات...
pip install -r requirements.txt --quiet
if errorlevel 1 (
    echo ❌ فشل في تثبيت المتطلبات
    echo جاري المحاولة مرة أخرى...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المتطلبات نهائياً
        pause
        exit /b 1
    )
)

echo ✅ تم تثبيت المتطلبات بنجاح

REM تشغيل النظام
echo.
echo 🚀 بدء تشغيل النظام...
echo 📱 سيتم فتح النظام في المتصفح تلقائياً
echo 🌐 أو يمكنك زيارة: http://localhost:5000
echo.
echo ⚠️  للإيقاف: اضغط Ctrl+C
echo.

python main.py

echo.
echo 👋 تم إيقاف النظام
pause
