# -*- coding: utf-8 -*-
"""
نموذج خلطة العلف - Feed Mix Model
يحتوي على تركيبة العلف والتحليل الغذائي
"""

from datetime import datetime, date
from typing import Dict, List, Optional
import json
from config.nutrition_standards import FEED_INGREDIENTS, WARNING_LIMITS

class FeedMix:
    """فئة خلطة العلف"""
    
    def __init__(self, mix_id: str, name: str, description: str = ""):
        self.mix_id = mix_id
        self.name = name
        self.description = description
        self.creation_date = date.today()
        self.last_modified = datetime.now()
        self.is_active = True
        
        # مكونات الخلطة: {ingredient_key: percentage}
        self.ingredients: Dict[str, float] = {}
        
        # التحليل الغذائي المحسوب
        self.nutritional_analysis: Dict[str, float] = {}
        
        # التكلفة
        self.cost_per_kg = 0.0
        self.cost_per_ton = 0.0
        
        # تقييم الخلطة
        self.quality_score = 0.0
        self.warnings: List[str] = []
        self.recommendations: List[str] = []
    
    def add_ingredient(self, ingredient_key: str, percentage: float, custom_data: dict = None) -> bool:
        """إضافة مكون إلى الخلطة"""
        # السماح بالمكونات المخصصة (تبدأ بـ custom_) أو المكونات العادية
        if ingredient_key not in FEED_INGREDIENTS and not ingredient_key.startswith('custom_'):
            return False

        if percentage <= 0 or percentage > 100:
            return False

        self.ingredients[ingredient_key] = percentage

        # حفظ بيانات المكون المخصص إذا كان متوفراً
        if custom_data and ingredient_key.startswith('custom_'):
            if not hasattr(self, 'custom_ingredients'):
                self.custom_ingredients = {}
            self.custom_ingredients[ingredient_key] = custom_data

        self.last_modified = datetime.now()
        self._calculate_nutritional_analysis()
        return True
    
    def remove_ingredient(self, ingredient_key: str) -> bool:
        """إزالة مكون من الخلطة"""
        if ingredient_key in self.ingredients:
            del self.ingredients[ingredient_key]
            self.last_modified = datetime.now()
            self._calculate_nutritional_analysis()
            return True
        return False
    
    def update_ingredient_percentage(self, ingredient_key: str, new_percentage: float) -> bool:
        """تحديث نسبة مكون في الخلطة"""
        if ingredient_key not in self.ingredients:
            return False
        
        if new_percentage <= 0:
            return self.remove_ingredient(ingredient_key)
        
        if new_percentage > 100:
            return False
        
        self.ingredients[ingredient_key] = new_percentage
        self.last_modified = datetime.now()
        self._calculate_nutritional_analysis()
        return True
    
    def normalize_percentages(self) -> bool:
        """تطبيع النسب المئوية لتصبح مجموعها 100%"""
        if not self.ingredients:
            return False
        
        total = sum(self.ingredients.values())
        if total == 0:
            return False
        
        # تطبيع النسب
        for ingredient_key in self.ingredients:
            self.ingredients[ingredient_key] = (self.ingredients[ingredient_key] / total) * 100
        
        self.last_modified = datetime.now()
        self._calculate_nutritional_analysis()
        return True
    
    def _calculate_nutritional_analysis(self):
        """حساب التحليل الغذائي للخلطة"""
        if not self.ingredients:
            self.nutritional_analysis = {}
            self.cost_per_kg = 0.0
            self.cost_per_ton = 0.0
            return
        
        # تطبيع النسب إذا لم تكن مجموعها 100%
        total_percentage = sum(self.ingredients.values())
        if total_percentage == 0:
            return
        
        # حساب القيم الغذائية
        analysis = {
            'dry_matter': 0.0,
            'crude_protein': 0.0,
            'energy_mcal': 0.0,
            'crude_fiber': 0.0,
            'calcium': 0.0,
            'phosphorus': 0.0
        }
        
        total_cost = 0.0
        
        for ingredient_key, percentage in self.ingredients.items():
            normalized_percentage = percentage / total_percentage

            if ingredient_key in FEED_INGREDIENTS:
                # مكون عادي
                ingredient_data = FEED_INGREDIENTS[ingredient_key]

                # حساب المساهمة في كل عنصر غذائي
                for nutrient in analysis:
                    if nutrient in ingredient_data:
                        analysis[nutrient] += ingredient_data[nutrient] * normalized_percentage

                # حساب التكلفة
                total_cost += ingredient_data['price_per_kg'] * normalized_percentage

            elif ingredient_key.startswith('custom_') and hasattr(self, 'custom_ingredients'):
                # مكون مخصص
                if ingredient_key in self.custom_ingredients:
                    custom_data = self.custom_ingredients[ingredient_key]

                    # حساب المساهمة من البيانات المخصصة
                    analysis['crude_protein'] += custom_data.get('crude_protein', 0) * normalized_percentage
                    analysis['energy_mcal'] += custom_data.get('energy_mcal', 0) * normalized_percentage
                    analysis['crude_fiber'] += custom_data.get('crude_fiber', 0) * normalized_percentage
                    analysis['calcium'] += custom_data.get('calcium', 0) * normalized_percentage
                    analysis['phosphorus'] += custom_data.get('phosphorus', 0) * normalized_percentage

                    # حساب التكلفة
                    total_cost += custom_data.get('price_per_kg', 0) * normalized_percentage
        
        self.nutritional_analysis = analysis
        self.cost_per_kg = round(total_cost, 3)
        self.cost_per_ton = round(total_cost * 1000, 2)
        
        # تقييم جودة الخلطة
        self._evaluate_mix_quality()
    
    def _evaluate_mix_quality(self):
        """تقييم جودة الخلطة وإنتاج التحذيرات والتوصيات"""
        self.warnings = []
        self.recommendations = []
        quality_points = 100
        
        if not self.nutritional_analysis:
            self.quality_score = 0
            return
        
        # فحص البروتين الخام
        protein = self.nutritional_analysis.get('crude_protein', 0)
        protein_limits = WARNING_LIMITS['crude_protein']
        if protein < protein_limits['min']:
            self.warnings.append(f"نسبة البروتين منخفضة ({protein:.1f}%) - الحد الأدنى {protein_limits['min']}%")
            quality_points -= 15
        elif protein > protein_limits['max']:
            self.warnings.append(f"نسبة البروتين مرتفعة ({protein:.1f}%) - الحد الأقصى {protein_limits['max']}%")
            quality_points -= 10
        
        # فحص الألياف الخام
        fiber = self.nutritional_analysis.get('crude_fiber', 0)
        fiber_limits = WARNING_LIMITS['crude_fiber']
        if fiber < fiber_limits['min']:
            self.warnings.append(f"نسبة الألياف منخفضة ({fiber:.1f}%) - الحد الأدنى {fiber_limits['min']}%")
            quality_points -= 20
        elif fiber > fiber_limits['max']:
            self.warnings.append(f"نسبة الألياف مرتفعة ({fiber:.1f}%) - الحد الأقصى {fiber_limits['max']}%")
            quality_points -= 10
        
        # فحص الطاقة
        energy = self.nutritional_analysis.get('energy_mcal', 0)
        energy_limits = WARNING_LIMITS['energy_mcal']
        if energy < energy_limits['min']:
            self.warnings.append(f"الطاقة منخفضة ({energy:.2f} ميجا كالوري/كغ) - الحد الأدنى {energy_limits['min']}")
            quality_points -= 15
        elif energy > energy_limits['max']:
            self.warnings.append(f"الطاقة مرتفعة ({energy:.2f} ميجا كالوري/كغ) - الحد الأقصى {energy_limits['max']}")
            quality_points -= 5
        
        # فحص الكالسيوم والفوسفور
        calcium = self.nutritional_analysis.get('calcium', 0)
        phosphorus = self.nutritional_analysis.get('phosphorus', 0)
        
        if calcium < WARNING_LIMITS['calcium']['min']:
            self.warnings.append(f"نسبة الكالسيوم منخفضة ({calcium:.2f}%)")
            quality_points -= 10
        
        if phosphorus < WARNING_LIMITS['phosphorus']['min']:
            self.warnings.append(f"نسبة الفوسفور منخفضة ({phosphorus:.2f}%)")
            quality_points -= 10
        
        # نسبة الكالسيوم إلى الفوسفور
        if calcium > 0 and phosphorus > 0:
            ca_p_ratio = calcium / phosphorus
            if ca_p_ratio < 1.2 or ca_p_ratio > 2.5:
                self.warnings.append(f"نسبة الكالسيوم إلى الفوسفور غير مثلى ({ca_p_ratio:.1f}:1)")
                quality_points -= 5
        
        # توصيات لتحسين الخلطة
        if protein < protein_limits['min']:
            self.recommendations.append("أضف كسبة فول الصويا أو كسبة بذرة القطن لزيادة البروتين")
        
        if fiber < fiber_limits['min']:
            self.recommendations.append("أضف دريس البرسيم أو نخالة القمح لزيادة الألياف")
        
        if energy < energy_limits['min']:
            self.recommendations.append("أضف الذرة الصفراء أو الشعير لزيادة الطاقة")
        
        if calcium < WARNING_LIMITS['calcium']['min']:
            self.recommendations.append("أضف الحجر الجيري لزيادة الكالسيوم")
        
        self.quality_score = max(0, min(100, quality_points))
    
    def get_ingredient_details(self) -> List[Dict]:
        """الحصول على تفاصيل مكونات الخلطة"""
        details = []
        total_percentage = sum(self.ingredients.values())
        
        for ingredient_key, percentage in self.ingredients.items():
            if ingredient_key not in FEED_INGREDIENTS:
                continue
            
            ingredient_data = FEED_INGREDIENTS[ingredient_key]
            normalized_percentage = percentage / total_percentage if total_percentage > 0 else 0
            
            details.append({
                'key': ingredient_key,
                'name_ar': ingredient_data['name_ar'],
                'name_en': ingredient_data['name_en'],
                'percentage': round(percentage, 2),
                'normalized_percentage': round(normalized_percentage * 100, 2),
                'kg_per_ton': round(normalized_percentage * 1000, 1),
                'cost_contribution': round(ingredient_data['price_per_kg'] * normalized_percentage, 3),
                'protein_contribution': round(ingredient_data['crude_protein'] * normalized_percentage, 2),
                'energy_contribution': round(ingredient_data['energy_mcal'] * normalized_percentage, 3)
            })
        
        return sorted(details, key=lambda x: x['percentage'], reverse=True)
    
    def calculate_daily_cost_per_cow(self, daily_intake_kg: float = 22.0) -> Dict:
        """حساب التكلفة اليومية للبقرة الواحدة"""
        daily_cost = self.cost_per_kg * daily_intake_kg
        monthly_cost = daily_cost * 30
        yearly_cost = daily_cost * 365
        
        return {
            'daily_cost': round(daily_cost, 3),
            'monthly_cost': round(monthly_cost, 2),
            'yearly_cost': round(yearly_cost, 2),
            'daily_intake_kg': daily_intake_kg
        }
    
    def to_dict(self) -> Dict:
        """تحويل بيانات الخلطة إلى قاموس"""
        result = {
            'mix_id': self.mix_id,
            'name': self.name,
            'description': self.description,
            'creation_date': self.creation_date.isoformat(),
            'last_modified': self.last_modified.isoformat(),
            'is_active': self.is_active,
            'ingredients': self.ingredients,
            'nutritional_analysis': self.nutritional_analysis,
            'cost_per_kg': self.cost_per_kg,
            'cost_per_ton': self.cost_per_ton,
            'quality_score': self.quality_score,
            'warnings': self.warnings,
            'recommendations': self.recommendations
        }

        # إضافة المكونات المخصصة إذا كانت موجودة
        if hasattr(self, 'custom_ingredients'):
            result['custom_ingredients'] = self.custom_ingredients

        return result
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'FeedMix':
        """إنشاء كائن خلطة من قاموس"""
        mix = cls(
            mix_id=data['mix_id'],
            name=data['name'],
            description=data['description']
        )

        mix.creation_date = date.fromisoformat(data['creation_date'])
        mix.last_modified = datetime.fromisoformat(data['last_modified'])
        mix.is_active = data['is_active']
        mix.ingredients = data['ingredients']
        mix.nutritional_analysis = data['nutritional_analysis']
        mix.cost_per_kg = data['cost_per_kg']
        mix.cost_per_ton = data['cost_per_ton']
        mix.quality_score = data['quality_score']
        mix.warnings = data['warnings']
        mix.recommendations = data['recommendations']

        # استعادة المكونات المخصصة إذا كانت موجودة
        if 'custom_ingredients' in data:
            mix.custom_ingredients = data['custom_ingredients']

        return mix
