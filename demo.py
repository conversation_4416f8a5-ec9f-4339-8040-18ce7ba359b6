#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عرض تجريبي لنظام إدارة مزرعة الأبقار الحلوب
Demo script for Dairy Farm Management System
"""

import os
import sys
import random
from datetime import date, datetime, timed<PERSON><PERSON>

def create_comprehensive_demo_data():
    """إنشاء بيانات تجريبية شاملة"""
    from database.db_manager import DatabaseManager
    from models.cow import Cow
    from models.feed_mix import FeedMix
    
    print("🎭 إنشاء بيانات تجريبية شاملة...")
    
    db_manager = DatabaseManager()
    
    # بيانات أبقار متنوعة
    demo_cows = [
        # أبقار هولشتاين عالية الإنتاج
        {'cow_id': 'H001', 'name': 'ملكة الحليب', 'breed': 'holstein', 'weight': 680, 'health': 'سليمة', 'milk_range': (28, 35)},
        {'cow_id': 'H002', 'name': 'النجمة البيضاء', 'breed': 'holstein', 'weight': 650, 'health': 'سليمة', 'milk_range': (25, 32)},
        {'cow_id': 'H003', 'name': 'الأميرة', 'breed': 'holstein', 'weight': 670, 'health': 'حامل', 'milk_range': (20, 28)},
        
        # أبقار فريزيان متوسطة الإنتاج
        {'cow_id': 'F001', 'name': 'الوردة الحمراء', 'breed': 'friesian', 'weight': 620, 'health': 'سليمة', 'milk_range': (22, 30)},
        {'cow_id': 'F002', 'name': 'نسمة الصباح', 'breed': 'friesian', 'weight': 610, 'health': 'سليمة', 'milk_range': (20, 28)},
        {'cow_id': 'F003', 'name': 'ضوء القمر', 'breed': 'friesian', 'weight': 630, 'health': 'مريضة', 'milk_range': (15, 22)},
        
        # أبقار جيرسي صغيرة الحجم
        {'cow_id': 'J001', 'name': 'الجوهرة الذهبية', 'breed': 'jersey', 'weight': 450, 'health': 'سليمة', 'milk_range': (18, 25)},
        {'cow_id': 'J002', 'name': 'نجمة الصحراء', 'breed': 'jersey', 'weight': 460, 'health': 'سليمة', 'milk_range': (16, 23)},
        
        # أبقار براون سويس
        {'cow_id': 'B001', 'name': 'الجبل الشامخ', 'breed': 'brown_swiss', 'weight': 600, 'health': 'سليمة', 'milk_range': (24, 31)},
        {'cow_id': 'B002', 'name': 'عبير الجبال', 'breed': 'brown_swiss', 'weight': 590, 'health': 'جافة', 'milk_range': (0, 0)},
        
        # أبقار محلية
        {'cow_id': 'L001', 'name': 'أصيلة البادية', 'breed': 'local', 'weight': 400, 'health': 'سليمة', 'milk_range': (12, 18)},
        {'cow_id': 'L002', 'name': 'كريمة الصحراء', 'breed': 'local', 'weight': 420, 'health': 'سليمة', 'milk_range': (10, 16)},
        {'cow_id': 'L003', 'name': 'بركة الخير', 'breed': 'local', 'weight': 380, 'health': 'حامل', 'milk_range': (8, 14)},
        
        # أبقار إضافية لتنويع البيانات
        {'cow_id': 'H004', 'name': 'سيدة القطيع', 'breed': 'holstein', 'weight': 690, 'health': 'سليمة', 'milk_range': (30, 38)},
        {'cow_id': 'F004', 'name': 'ريحانة المراعي', 'breed': 'friesian', 'weight': 640, 'health': 'سليمة', 'milk_range': (23, 29)},
    ]
    
    print(f"إنشاء {len(demo_cows)} بقرة تجريبية...")
    
    for cow_data in demo_cows:
        # حساب تاريخ الميلاد (عمر عشوائي بين 2-6 سنوات)
        age_years = random.uniform(2, 6)
        birth_date = date.today() - timedelta(days=int(age_years * 365))
        
        # حساب تاريخ آخر ولادة
        if cow_data['health'] != 'جافة':
            days_since_calving = random.randint(30, 250)
            last_calving = date.today() - timedelta(days=days_since_calving)
        else:
            last_calving = None
        
        cow = Cow(
            cow_id=cow_data['cow_id'],
            name=cow_data['name'],
            breed=cow_data['breed'],
            birth_date=birth_date,
            weight=cow_data['weight']
        )
        
        cow.last_calving_date = last_calving
        cow.health_status = cow_data['health']
        cow.lactation_number = random.randint(1, 4)
        cow.daily_feed_intake = cow.weight * 0.035  # 3.5% من وزن الجسم
        cow.body_condition_score = random.uniform(2.5, 4.0)
        
        # إضافة ملاحظات عشوائية
        notes_options = [
            "بقرة ممتازة الإنتاج",
            "تحتاج متابعة خاصة",
            "استجابة جيدة للعلف الجديد",
            "بقرة هادئة ومطيعة",
            "تحتاج فحص بيطري دوري",
            ""
        ]
        cow.notes = random.choice(notes_options)
        
        # إضافة سجلات حليب للأيام الماضية
        if cow_data['health'] in ['سليمة', 'حامل', 'مريضة']:
            for i in range(30):  # آخر 30 يوم
                record_date = date.today() - timedelta(days=i)
                
                # تحديد نطاق الإنتاج حسب الحالة
                min_milk, max_milk = cow_data['milk_range']
                
                if cow_data['health'] == 'مريضة':
                    # تقليل الإنتاج للأبقار المريضة
                    min_milk *= 0.7
                    max_milk *= 0.8
                elif cow_data['health'] == 'حامل':
                    # تقليل الإنتاج للأبقار الحامل
                    min_milk *= 0.8
                    max_milk *= 0.9
                
                # إضافة تباين يومي طبيعي
                daily_variation = random.uniform(0.85, 1.15)
                morning_milk = random.uniform(min_milk * 0.6, max_milk * 0.6) * daily_variation
                evening_milk = random.uniform(min_milk * 0.4, max_milk * 0.4) * daily_variation
                
                # إضافة تأثير الطقس والموسم
                if i < 7:  # الأسبوع الأخير
                    seasonal_factor = random.uniform(0.95, 1.05)
                    morning_milk *= seasonal_factor
                    evening_milk *= seasonal_factor
                
                cow.add_milk_record(record_date, morning_milk, evening_milk)
        
        try:
            db_manager.add_cow(cow)
            print(f"✅ {cow.name} ({cow.cow_id}) - {cow.breed}")
        except:
            print(f"⚠️  {cow.name} موجودة مسبقاً")
    
    # إنشاء خلطات علف متنوعة
    demo_mixes = [
        {
            'mix_id': 'PREMIUM_001',
            'name': 'خلطة البريميوم للإنتاج العالي',
            'description': 'خلطة متطورة للأبقار عالية الإنتاج مع أفضل المكونات',
            'ingredients': {
                'corn': 40.0,
                'soybean_meal': 25.0,
                'alfalfa_hay': 20.0,
                'wheat_bran': 10.0,
                'limestone': 3.0,
                'salt': 2.0
            }
        },
        {
            'mix_id': 'ECONOMIC_001',
            'name': 'الخلطة الاقتصادية المتوازنة',
            'description': 'خلطة اقتصادية مع توازن غذائي ممتاز',
            'ingredients': {
                'corn': 30.0,
                'barley': 25.0,
                'alfalfa_hay': 25.0,
                'rice_bran': 15.0,
                'cotton_seed_meal': 3.0,
                'limestone': 1.5,
                'salt': 0.5
            }
        },
        {
            'mix_id': 'DRY_COW_001',
            'name': 'خلطة الأبقار الجافة',
            'description': 'خلطة مخصصة للأبقار في فترة الجفاف',
            'ingredients': {
                'barley': 35.0,
                'alfalfa_hay': 45.0,
                'wheat_bran': 15.0,
                'rice_bran': 3.0,
                'limestone': 1.5,
                'salt': 0.5
            }
        },
        {
            'mix_id': 'ORGANIC_001',
            'name': 'الخلطة العضوية الطبيعية',
            'description': 'خلطة عضوية 100% للإنتاج الطبيعي',
            'ingredients': {
                'corn': 35.0,
                'alfalfa_hay': 35.0,
                'soybean_meal': 20.0,
                'wheat_bran': 8.0,
                'limestone': 1.5,
                'salt': 0.5
            }
        },
        {
            'mix_id': 'STARTER_001',
            'name': 'خلطة البداية للعجول',
            'description': 'خلطة مخصصة للعجول الصغيرة',
            'ingredients': {
                'corn': 45.0,
                'soybean_meal': 30.0,
                'alfalfa_hay': 15.0,
                'wheat_bran': 8.0,
                'limestone': 1.5,
                'salt': 0.5
            }
        }
    ]
    
    print(f"\nإنشاء {len(demo_mixes)} خلطة علف متنوعة...")
    
    for mix_data in demo_mixes:
        feed_mix = FeedMix(
            mix_id=mix_data['mix_id'],
            name=mix_data['name'],
            description=mix_data['description']
        )
        
        for ingredient, percentage in mix_data['ingredients'].items():
            feed_mix.add_ingredient(ingredient, percentage)
        
        try:
            db_manager.add_feed_mix(feed_mix)
            print(f"✅ {feed_mix.name}")
        except:
            print(f"⚠️  {feed_mix.name} موجودة مسبقاً")
    
    # إضافة أحداث وتنبيهات تجريبية
    print("\nإضافة أحداث وتنبيهات...")
    
    events = [
        ("تطعيم", "تم تطعيم القطيع ضد الحمى القلاعية", "info"),
        ("فحص بيطري", "فحص دوري للأبقار المريضة", "warning"),
        ("تغيير علف", "تم تغيير خلطة العلف للأبقار عالية الإنتاج", "info"),
        ("ولادة", "ولادة ناجحة للبقرة H003", "success"),
        ("انخفاض إنتاج", "انخفاض ملحوظ في إنتاج البقرة F003", "warning"),
    ]
    
    for event_type, description, severity in events:
        try:
            db_manager.add_event(event_type, description, severity)
            print(f"✅ {description}")
        except:
            pass
    
    print("\n🎉 تم إنشاء البيانات التجريبية الشاملة بنجاح!")
    print(f"📊 إجمالي الأبقار: {len(demo_cows)}")
    print(f"🌾 إجمالي خلطات العلف: {len(demo_mixes)}")
    print(f"📅 سجلات الحليب: آخر 30 يوم لكل بقرة منتجة")
    print(f"🔔 الأحداث والتنبيهات: {len(events)}")

def show_demo_info():
    """عرض معلومات العرض التجريبي"""
    print("=" * 70)
    print("🎭 العرض التجريبي لنظام إدارة مزرعة الأبقار الحلوب")
    print("=" * 70)
    
    print("\n📋 ما يتضمنه العرض التجريبي:")
    print("• 15 بقرة من سلالات مختلفة (هولشتاين، فريزيان، جيرسي، براون سويس، محلي)")
    print("• 5 خلطات علف متنوعة (بريميوم، اقتصادية، أبقار جافة، عضوية، عجول)")
    print("• سجلات حليب يومية لآخر 30 يوم")
    print("• أحداث وتنبيهات متنوعة")
    print("• بيانات واقعية تحاكي مزرعة حقيقية")
    
    print("\n🎯 الهدف من العرض التجريبي:")
    print("• استكشاف جميع ميزات النظام")
    print("• فهم كيفية إدارة القطيع والأعلاف")
    print("• تجربة التقارير والإحصائيات")
    print("• تقييم فعالية النظام قبل الاستخدام الفعلي")
    
    print("\n⚠️  ملاحظة مهمة:")
    print("هذه بيانات تجريبية فقط ولا تمثل مزرعة حقيقية")
    print("يمكن حذف هذه البيانات وإدخال بياناتك الحقيقية لاحقاً")

def main():
    """الدالة الرئيسية للعرض التجريبي"""
    show_demo_info()
    
    response = input("\n❓ هل تريد إنشاء البيانات التجريبية؟ (y/n): ").lower().strip()
    
    if response in ['y', 'yes', 'نعم', 'ن']:
        try:
            create_comprehensive_demo_data()
            
            print("\n" + "=" * 70)
            print("✨ العرض التجريبي جاهز!")
            print("=" * 70)
            
            print("\n🚀 لتشغيل النظام:")
            print("python main.py")
            
            print("\n🌐 ثم زيارة: http://localhost:5000")
            
            print("\n📱 جرب الميزات التالية:")
            print("• لوحة المعلومات: إحصائيات شاملة")
            print("• إدارة القطيع: استعراض وتعديل بيانات الأبقار")
            print("• تتبع الحليب: مراجعة سجلات الإنتاج")
            print("• إدارة الأعلاف: تحليل خلطات العلف")
            print("• التقارير: إنشاء تقارير PDF وExcel")
            
            # سؤال عن التشغيل المباشر
            run_response = input("\n❓ هل تريد تشغيل النظام الآن؟ (y/n): ").lower().strip()
            if run_response in ['y', 'yes', 'نعم', 'ن']:
                print("\n🚀 بدء تشغيل النظام...")
                from main import main as run_main
                run_main()
            
        except Exception as e:
            print(f"\n❌ خطأ في إنشاء البيانات التجريبية: {e}")
    else:
        print("\n👋 تم إلغاء العرض التجريبي")

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف العرض التجريبي")
    except Exception as e:
        print(f"\n❌ خطأ: {e}")
    
    input("\nاضغط Enter للخروج...")
