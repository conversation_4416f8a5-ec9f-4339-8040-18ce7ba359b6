#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد وتثبيت نظام إدارة مزرعة الأبقار الحلوب
Setup and installation script for Dairy Farm Management System
"""

import os
import sys
import subprocess
import platform

def check_python_version():
    """التحقق من إصدار Python"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
        print(f"الإصدار الحالي: {version.major}.{version.minor}.{version.micro}")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} - متوافق")
    return True

def install_requirements():
    """تثبيت المتطلبات"""
    print("📥 تثبيت المتطلبات...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ تم تثبيت المتطلبات بنجاح")
        return True
    except subprocess.CalledProcessError:
        print("❌ فشل في تثبيت المتطلبات")
        return False

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = [
        'uploads',
        'reports',
        'static/charts',
        'backups'
    ]
    
    print("📁 إنشاء المجلدات...")
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ تم إنشاء مجلد: {directory}")

def test_imports():
    """اختبار استيراد المكتبات الأساسية"""
    print("🧪 اختبار المكتبات...")
    
    required_modules = [
        'flask',
        'matplotlib',
        'pandas',
        'numpy',
        'reportlab',
        'openpyxl'
    ]
    
    failed_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module}")
            failed_modules.append(module)
    
    if failed_modules:
        print(f"\n❌ فشل في استيراد: {', '.join(failed_modules)}")
        return False
    
    print("✅ جميع المكتبات متاحة")
    return True

def create_desktop_shortcut():
    """إنشاء اختصار على سطح المكتب"""
    system = platform.system()
    
    if system == "Windows":
        try:
            import winshell
            from win32com.client import Dispatch
            
            desktop = winshell.desktop()
            path = os.path.join(desktop, "نظام إدارة مزرعة الأبقار.lnk")
            target = os.path.join(os.getcwd(), "run.bat")
            wDir = os.getcwd()
            icon = target
            
            shell = Dispatch('WScript.Shell')
            shortcut = shell.CreateShortCut(path)
            shortcut.Targetpath = target
            shortcut.WorkingDirectory = wDir
            shortcut.IconLocation = icon
            shortcut.save()
            
            print("✅ تم إنشاء اختصار على سطح المكتب")
        except ImportError:
            print("⚠️  لم يتم إنشاء اختصار سطح المكتب (مكتبات Windows غير متاحة)")
        except Exception as e:
            print(f"⚠️  لم يتم إنشاء اختصار سطح المكتب: {e}")
    
    elif system == "Linux":
        try:
            desktop_file = f"""[Desktop Entry]
Name=نظام إدارة مزرعة الأبقار
Comment=Dairy Farm Management System
Exec={os.path.join(os.getcwd(), 'run.sh')}
Icon={os.path.join(os.getcwd(), 'static', 'icon.png')}
Terminal=true
Type=Application
Categories=Office;
"""
            
            desktop_path = os.path.expanduser("~/Desktop/dairy-farm.desktop")
            with open(desktop_path, 'w', encoding='utf-8') as f:
                f.write(desktop_file)
            
            os.chmod(desktop_path, 0o755)
            print("✅ تم إنشاء اختصار على سطح المكتب")
        except Exception as e:
            print(f"⚠️  لم يتم إنشاء اختصار سطح المكتب: {e}")

def main():
    """الدالة الرئيسية للإعداد"""
    print("=" * 60)
    print("🐄 إعداد نظام إدارة مزرعة الأبقار الحلوب")
    print("   Dairy Farm Management System Setup")
    print("=" * 60)
    
    # التحقق من إصدار Python
    if not check_python_version():
        input("اضغط Enter للخروج...")
        return False
    
    # إنشاء المجلدات
    create_directories()
    
    # تثبيت المتطلبات
    if not install_requirements():
        input("اضغط Enter للخروج...")
        return False
    
    # اختبار المكتبات
    if not test_imports():
        print("\n⚠️  بعض المكتبات غير متاحة، لكن يمكن المتابعة")
    
    # إنشاء اختصار سطح المكتب
    create_desktop_shortcut()
    
    print("\n" + "=" * 60)
    print("🎉 تم إعداد النظام بنجاح!")
    print("=" * 60)
    
    print("\n📋 خطوات التشغيل:")
    if platform.system() == "Windows":
        print("1. انقر نقراً مزدوجاً على 'run.bat'")
        print("2. أو شغل الأمر: python main.py")
    else:
        print("1. شغل الأمر: ./run.sh")
        print("2. أو شغل الأمر: python3 main.py")
    
    print("\n🌐 سيتم فتح النظام على: http://localhost:5000")
    
    print("\n📚 للمساعدة:")
    print("- راجع ملف README.md")
    print("- تحقق من مجلد docs/ للوثائق")
    
    print("\n✨ نتمنى لك تجربة ممتعة!")
    
    # سؤال عن التشغيل المباشر
    response = input("\n❓ هل تريد تشغيل النظام الآن؟ (y/n): ").lower().strip()
    if response in ['y', 'yes', 'نعم', 'ن']:
        print("\n🚀 بدء تشغيل النظام...")
        try:
            from main import main as run_main
            run_main()
        except KeyboardInterrupt:
            print("\n👋 تم إيقاف النظام")
        except Exception as e:
            print(f"\n❌ خطأ في التشغيل: {e}")
    
    return True

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 تم إلغاء الإعداد")
    except Exception as e:
        print(f"\n❌ خطأ في الإعداد: {e}")
        input("اضغط Enter للخروج...")
    
    input("\nاضغط Enter للخروج...")
