# -*- coding: utf-8 -*-
"""
تطبيق Flask الرئيسي لنظام إدارة مزرعة الأبقار الحلوب
Main Flask Application for Dairy Farm Management System
"""

from flask import Flask, render_template, request, jsonify, redirect, url_for, flash, send_file
from flask_sqlalchemy import SQLAlchemy
from datetime import date, datetime, timedelta
import os
import json
from werkzeug.utils import secure_filename

from database.db_manager import DatabaseManager
from models.cow import Cow
from models.feed_mix import FeedMix
from utils.nutrition_analyzer import NutritionAnalyzer
from utils.report_generator import ReportGenerator
from config.nutrition_standards import FEED_INGREDIENTS, BREED_WEIGHTS

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'dairy_farm_secret_key_2024'
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# إنشاء مجلدات ضرورية
os.makedirs('uploads', exist_ok=True)
os.makedirs('reports', exist_ok=True)
os.makedirs('static/charts', exist_ok=True)

# إنشاء كائنات النظام
db_manager = DatabaseManager()
nutrition_analyzer = NutritionAnalyzer()
report_generator = ReportGenerator()

def calculate_monthly_statistics(monthly_records, first_day, last_day):
    """حساب الإحصائيات الشهرية للحليب"""
    if not monthly_records:
        return {
            'total_milk': 0,
            'average_daily': 0,
            'best_day': {'date': first_day, 'total': 0},
            'worst_day': {'date': first_day, 'total': 0},
            'total_days': (last_day - first_day).days + 1,
            'productive_days': 0,
            'cow_performance': {}
        }

    # تجميع البيانات حسب التاريخ
    daily_totals = {}
    cow_totals = {}

    for record in monthly_records:
        record_date = record['record_date']
        daily_total = record['daily_total']
        cow_id = record['cow_id']

        # إجمالي يومي
        if record_date not in daily_totals:
            daily_totals[record_date] = 0
        daily_totals[record_date] += daily_total

        # إجمالي لكل بقرة
        if cow_id not in cow_totals:
            cow_totals[cow_id] = {'total': 0, 'days': 0, 'cow_name': record.get('cow_name', cow_id)}
        cow_totals[cow_id]['total'] += daily_total
        cow_totals[cow_id]['days'] += 1

    # حساب الإحصائيات
    total_milk = sum(daily_totals.values())
    total_days = (last_day - first_day).days + 1
    productive_days = len(daily_totals)
    average_daily = total_milk / productive_days if productive_days > 0 else 0

    # أفضل وأسوأ يوم
    if daily_totals:
        best_date = max(daily_totals, key=daily_totals.get)
        worst_date = min(daily_totals, key=daily_totals.get)
        best_day = {'date': best_date, 'total': daily_totals[best_date]}
        worst_day = {'date': worst_date, 'total': daily_totals[worst_date]}
    else:
        best_day = {'date': first_day, 'total': 0}
        worst_day = {'date': first_day, 'total': 0}

    # أداء الأبقار
    cow_performance = []
    for cow_id, data in cow_totals.items():
        cow_performance.append({
            'cow_id': cow_id,
            'cow_name': data['cow_name'],
            'total_milk': data['total'],
            'average_daily': data['total'] / data['days'] if data['days'] > 0 else 0,
            'productive_days': data['days']
        })

    cow_performance.sort(key=lambda x: x['total_milk'], reverse=True)

    return {
        'total_milk': round(total_milk, 2),
        'average_daily': round(average_daily, 2),
        'best_day': best_day,
        'worst_day': worst_day,
        'total_days': total_days,
        'productive_days': productive_days,
        'cow_performance': cow_performance,
        'daily_totals': daily_totals
    }

def prepare_monthly_chart_data(monthly_records, first_day, last_day):
    """إعداد بيانات الرسم البياني للشهر"""
    # تجميع البيانات حسب التاريخ
    daily_data = {}
    cow_data = {}

    for record in monthly_records:
        record_date = record['record_date']
        daily_total = record['daily_total']
        cow_id = record['cow_id']
        cow_name = record.get('cow_name', cow_id)

        # البيانات اليومية
        if record_date not in daily_data:
            daily_data[record_date] = 0
        daily_data[record_date] += daily_total

        # بيانات الأبقار
        if cow_id not in cow_data:
            cow_data[cow_id] = {'name': cow_name, 'total': 0, 'daily': {}}
        cow_data[cow_id]['total'] += daily_total
        cow_data[cow_id]['daily'][record_date] = cow_data[cow_id]['daily'].get(record_date, 0) + daily_total

    # إنشاء قائمة بجميع أيام الشهر
    current_date = first_day
    all_dates = []
    daily_totals = []

    while current_date <= last_day:
        date_str = current_date.isoformat()
        all_dates.append(date_str)
        daily_totals.append(daily_data.get(date_str, 0))
        current_date += timedelta(days=1)

    # بيانات الأبقار للرسم البياني
    cow_chart_data = []
    for cow_id, data in cow_data.items():
        cow_daily = []
        for date_str in all_dates:
            cow_daily.append(data['daily'].get(date_str, 0))

        cow_chart_data.append({
            'cow_id': cow_id,
            'cow_name': data['name'],
            'data': cow_daily,
            'total': data['total']
        })

    cow_chart_data.sort(key=lambda x: x['total'], reverse=True)

    return {
        'dates': all_dates,
        'daily_totals': daily_totals,
        'cow_data': cow_chart_data[:10]  # أفضل 10 أبقار فقط
    }

def calculate_cow_monthly_statistics(cow_records, first_day, last_day):
    """حساب الإحصائيات الشهرية لبقرة واحدة"""
    if not cow_records:
        return {
            'total_milk': 0,
            'average_daily': 0,
            'best_day': {'date': first_day, 'morning': 0, 'evening': 0, 'total': 0},
            'worst_day': {'date': first_day, 'morning': 0, 'evening': 0, 'total': 0},
            'total_days': (last_day - first_day).days + 1,
            'productive_days': 0,
            'missing_days': [],
            'trend_analysis': 'insufficient_data'
        }

    # تجميع البيانات حسب التاريخ
    daily_data = {}
    total_milk = 0

    for record in cow_records:
        record_date = record['record_date']
        daily_total = record['daily_total']
        morning_milk = record['morning_milk']
        evening_milk = record['evening_milk']

        daily_data[record_date] = {
            'morning': morning_milk,
            'evening': evening_milk,
            'total': daily_total
        }
        total_milk += daily_total

    # حساب الإحصائيات
    total_days = (last_day - first_day).days + 1
    productive_days = len(daily_data)
    average_daily = total_milk / productive_days if productive_days > 0 else 0

    # أفضل وأسوأ يوم
    if daily_data:
        best_date = max(daily_data, key=lambda x: daily_data[x]['total'])
        worst_date = min(daily_data, key=lambda x: daily_data[x]['total'])
        best_day = {
            'date': best_date,
            'morning': daily_data[best_date]['morning'],
            'evening': daily_data[best_date]['evening'],
            'total': daily_data[best_date]['total']
        }
        worst_day = {
            'date': worst_date,
            'morning': daily_data[worst_date]['morning'],
            'evening': daily_data[worst_date]['evening'],
            'total': daily_data[worst_date]['total']
        }
    else:
        best_day = {'date': first_day, 'morning': 0, 'evening': 0, 'total': 0}
        worst_day = {'date': first_day, 'morning': 0, 'evening': 0, 'total': 0}

    # الأيام المفقودة
    missing_days = []
    current_date = first_day
    while current_date <= last_day:
        if current_date.isoformat() not in daily_data:
            missing_days.append(current_date.isoformat())
        current_date += timedelta(days=1)

    # تحليل الاتجاه
    trend_analysis = 'stable'
    if len(cow_records) >= 7:
        recent_records = cow_records[-7:]
        older_records = cow_records[-14:-7] if len(cow_records) >= 14 else []

        if older_records:
            recent_avg = sum(r['daily_total'] for r in recent_records) / len(recent_records)
            older_avg = sum(r['daily_total'] for r in older_records) / len(older_records)
            change_percent = ((recent_avg - older_avg) / older_avg) * 100 if older_avg > 0 else 0

            if change_percent > 5:
                trend_analysis = 'increasing'
            elif change_percent < -5:
                trend_analysis = 'decreasing'

    return {
        'total_milk': round(total_milk, 2),
        'average_daily': round(average_daily, 2),
        'best_day': best_day,
        'worst_day': worst_day,
        'total_days': total_days,
        'productive_days': productive_days,
        'missing_days': missing_days,
        'trend_analysis': trend_analysis,
        'daily_data': daily_data
    }

def prepare_cow_monthly_chart_data(cow_records, first_day, last_day):
    """إعداد بيانات الرسم البياني لبقرة واحدة"""
    # تجميع البيانات حسب التاريخ
    daily_data = {}

    for record in cow_records:
        record_date = record['record_date']
        daily_data[record_date] = {
            'morning': record['morning_milk'],
            'evening': record['evening_milk'],
            'total': record['daily_total']
        }

    # إنشاء قائمة بجميع أيام الشهر
    current_date = first_day
    all_dates = []
    morning_data = []
    evening_data = []
    total_data = []

    while current_date <= last_day:
        date_str = current_date.isoformat()
        all_dates.append(date_str)

        if date_str in daily_data:
            morning_data.append(daily_data[date_str]['morning'])
            evening_data.append(daily_data[date_str]['evening'])
            total_data.append(daily_data[date_str]['total'])
        else:
            morning_data.append(0)
            evening_data.append(0)
            total_data.append(0)

        current_date += timedelta(days=1)

    return {
        'dates': all_dates,
        'morning_data': morning_data,
        'evening_data': evening_data,
        'total_data': total_data
    }

@app.route('/')
def index():
    """الصفحة الرئيسية - لوحة المعلومات"""
    try:
        # إحصائيات القطيع
        herd_stats = db_manager.get_herd_statistics()
        
        # إنتاج اليوم
        today_summary = db_manager.get_daily_milk_summary(date.today())
        
        # الأحداث الأخيرة
        recent_events = db_manager.get_recent_events(days=7)
        
        # أعلى منتجات الحليب
        all_cows = db_manager.get_all_cows()
        top_producers = sorted(all_cows, key=lambda x: x.average_daily_milk, reverse=True)[:5]
        
        # خلطات العلف النشطة
        active_mixes = db_manager.get_all_feed_mixes()[:3]
        
        return render_template('dashboard.html',
                             herd_stats=herd_stats,
                             today_summary=today_summary,
                             recent_events=recent_events,
                             top_producers=top_producers,
                             active_mixes=active_mixes,
                             current_date=date.today())
    
    except Exception as e:
        flash(f'خطأ في تحميل البيانات: {str(e)}', 'error')
        return render_template('dashboard.html',
                             herd_stats={},
                             today_summary={},
                             recent_events=[],
                             top_producers=[],
                             active_mixes=[],
                             current_date=date.today())

@app.route('/herd')
def herd_management():
    """صفحة إدارة القطيع"""
    try:
        cows = db_manager.get_all_cows(active_only=False)
        breed_filter = request.args.get('breed', '')
        status_filter = request.args.get('status', '')
        search_query = request.args.get('search', '')

        # تطبيق الفلاتر
        if breed_filter:
            cows = [cow for cow in cows if cow.breed == breed_filter]

        if status_filter:
            cows = [cow for cow in cows if cow.health_status == status_filter]

        if search_query:
            search_query = search_query.lower()
            cows = [cow for cow in cows if
                   search_query in cow.cow_id.lower() or
                   search_query in cow.name.lower()]

        # تحويل الأبقار إلى قواميس للـ JSON
        cows_data = [cow.to_dict() for cow in cows]

        return render_template('herd_management.html',
                             cows=cows,
                             cows_data=cows_data,
                             breed_weights=BREED_WEIGHTS,
                             breed_filter=breed_filter,
                             status_filter=status_filter,
                             search_query=search_query)

    except Exception as e:
        flash(f'خطأ في تحميل بيانات القطيع: {str(e)}', 'error')
        return render_template('herd_management.html',
                             cows=[],
                             cows_data=[],
                             breed_weights=BREED_WEIGHTS,
                             breed_filter='',
                             status_filter='',
                             search_query='')

@app.route('/herd/add', methods=['GET', 'POST'])
def add_cow():
    """إضافة بقرة جديدة"""
    if request.method == 'POST':
        try:
            cow_data = request.form.to_dict()
            
            # التحقق من البيانات المطلوبة
            if not cow_data.get('cow_id'):
                flash('رقم البقرة مطلوب', 'error')
                return redirect(url_for('add_cow'))
            
            # إنشاء كائن البقرة
            cow = Cow(
                cow_id=cow_data['cow_id'],
                name=cow_data.get('name', f"بقرة رقم {cow_data['cow_id']}"),
                breed=cow_data.get('breed', 'local'),
                birth_date=datetime.strptime(cow_data['birth_date'], '%Y-%m-%d').date(),
                weight=float(cow_data.get('weight', 600))
            )
            
            # إضافة البيانات الإضافية
            if cow_data.get('last_calving_date'):
                cow.last_calving_date = datetime.strptime(cow_data['last_calving_date'], '%Y-%m-%d').date()
            
            cow.lactation_number = int(cow_data.get('lactation_number', 1))
            cow.health_status = cow_data.get('health_status', 'سليمة')
            cow.daily_feed_intake = float(cow_data.get('daily_feed_intake', 22.0))
            cow.body_condition_score = float(cow_data.get('body_condition_score', 3.0))
            cow.notes = cow_data.get('notes', '')
            cow.is_active = cow_data.get('is_active') == 'on'
            
            # حفظ في قاعدة البيانات
            if db_manager.add_cow(cow):
                flash('تم إضافة البقرة بنجاح', 'success')
                return redirect(url_for('herd_management'))
            else:
                flash('فشل في إضافة البقرة - قد يكون الرقم مستخدم', 'error')
        
        except Exception as e:
            flash(f'خطأ في إضافة البقرة: {str(e)}', 'error')
    
    return render_template('add_cow.html', breed_weights=BREED_WEIGHTS)

@app.route('/herd/edit/<cow_id>', methods=['GET', 'POST'])
def edit_cow(cow_id):
    """تعديل بيانات البقرة"""
    cow = db_manager.get_cow(cow_id)
    if not cow:
        flash('البقرة غير موجودة', 'error')
        return redirect(url_for('herd_management'))
    
    if request.method == 'POST':
        try:
            cow_data = request.form.to_dict()
            
            # تحديث البيانات
            cow.name = cow_data.get('name', cow.name)
            cow.breed = cow_data.get('breed', cow.breed)
            cow.birth_date = datetime.strptime(cow_data['birth_date'], '%Y-%m-%d').date()
            cow.weight = float(cow_data.get('weight', cow.weight))
            
            if cow_data.get('last_calving_date'):
                cow.last_calving_date = datetime.strptime(cow_data['last_calving_date'], '%Y-%m-%d').date()
            
            cow.lactation_number = int(cow_data.get('lactation_number', cow.lactation_number))
            cow.health_status = cow_data.get('health_status', cow.health_status)
            cow.daily_feed_intake = float(cow_data.get('daily_feed_intake', cow.daily_feed_intake))
            cow.body_condition_score = float(cow_data.get('body_condition_score', cow.body_condition_score))
            cow.notes = cow_data.get('notes', cow.notes)
            cow.is_active = cow_data.get('is_active') == 'on'
            
            # حفظ التحديثات
            if db_manager.update_cow(cow):
                flash('تم تحديث البقرة بنجاح', 'success')
                return redirect(url_for('herd_management'))
            else:
                flash('فشل في تحديث البقرة', 'error')
        
        except Exception as e:
            flash(f'خطأ في تحديث البقرة: {str(e)}', 'error')
    
    return render_template('edit_cow.html', cow=cow, breed_weights=BREED_WEIGHTS)

@app.route('/herd/delete/<cow_id>', methods=['POST'])
def delete_cow(cow_id):
    """حذف (تعطيل) البقرة"""
    try:
        cow = db_manager.get_cow(cow_id)
        if cow:
            cow.is_active = False
            if db_manager.update_cow(cow):
                flash('تم تعطيل البقرة بنجاح', 'success')
            else:
                flash('فشل في تعطيل البقرة', 'error')
        else:
            flash('البقرة غير موجودة', 'error')
    except Exception as e:
        flash(f'خطأ في تعطيل البقرة: {str(e)}', 'error')
    
    return redirect(url_for('herd_management'))

@app.route('/herd/reactivate/<cow_id>', methods=['POST'])
def reactivate_cow(cow_id):
    """إعادة تفعيل البقرة"""
    try:
        cow = db_manager.get_cow(cow_id)
        if cow:
            cow.is_active = True
            if db_manager.update_cow(cow):
                flash('تم إعادة تفعيل البقرة بنجاح', 'success')
            else:
                flash('فشل في إعادة تفعيل البقرة', 'error')
        else:
            flash('البقرة غير موجودة', 'error')
    except Exception as e:
        flash(f'خطأ في إعادة تفعيل البقرة: {str(e)}', 'error')

    return redirect(url_for('herd_management'))

@app.route('/herd/delete_permanently/<cow_id>', methods=['POST'])
def delete_cow_permanently(cow_id):
    """حذف البقرة نهائياً من قاعدة البيانات"""
    try:
        cow = db_manager.get_cow(cow_id)
        if cow:
            if db_manager.delete_cow_permanently(cow_id):
                flash('تم حذف البقرة نهائياً من النظام', 'success')
                # إضافة حدث للسجل
                db_manager.add_event(
                    event_type='cow_deleted',
                    description=f'تم حذف البقرة {cow.name} ({cow_id}) نهائياً من النظام',
                    severity='warning'
                )
            else:
                flash('فشل في حذف البقرة', 'error')
        else:
            flash('البقرة غير موجودة', 'error')
    except Exception as e:
        flash(f'خطأ في حذف البقرة: {str(e)}', 'error')

    return redirect(url_for('herd_management'))

@app.route('/feed')
def feed_management():
    """صفحة إدارة الأعلاف"""
    try:
        feed_mixes = db_manager.get_all_feed_mixes(active_only=False)
        return render_template('feed_management.html', 
                             feed_mixes=feed_mixes,
                             feed_ingredients=FEED_INGREDIENTS)
    except Exception as e:
        flash(f'خطأ في تحميل بيانات الأعلاف: {str(e)}', 'error')
        return render_template('feed_management.html', feed_mixes=[])

@app.route('/feed/add', methods=['GET', 'POST'])
def add_feed_mix():
    """إضافة خلطة علف جديدة"""
    if request.method == 'POST':
        try:
            mix_data = request.form.to_dict()
            
            # إنشاء خلطة جديدة
            feed_mix = FeedMix(
                mix_id=mix_data['mix_id'],
                name=mix_data['name'],
                description=mix_data.get('description', '')
            )
            
            # إضافة المكونات
            for key, value in mix_data.items():
                if key.startswith('ingredient_') and value:
                    ingredient_key = key.replace('ingredient_', '')
                    percentage = float(value)
                    if percentage > 0:
                        feed_mix.add_ingredient(ingredient_key, percentage)
            
            # تطبيع النسب
            feed_mix.normalize_percentages()
            
            # حفظ في قاعدة البيانات
            if db_manager.add_feed_mix(feed_mix):
                flash('تم إضافة خلطة العلف بنجاح', 'success')
                return redirect(url_for('feed_management'))
            else:
                flash('فشل في إضافة خلطة العلف - قد يكون الرقم مستخدم', 'error')
        
        except Exception as e:
            flash(f'خطأ في إضافة خلطة العلف: {str(e)}', 'error')
    
    return render_template('add_feed_mix.html', feed_ingredients=FEED_INGREDIENTS)

@app.route('/feed/edit/<mix_id>', methods=['GET', 'POST'])
def edit_feed_mix(mix_id):
    """تعديل خلطة العلف"""
    feed_mix = db_manager.get_feed_mix(mix_id)
    if not feed_mix:
        flash('خلطة العلف غير موجودة', 'error')
        return redirect(url_for('feed_management'))
    
    if request.method == 'POST':
        try:
            mix_data = request.form.to_dict()
            
            # تحديث البيانات الأساسية
            feed_mix.name = mix_data['name']
            feed_mix.description = mix_data.get('description', '')
            
            # مسح المكونات الحالية وإضافة الجديدة
            feed_mix.ingredients = {}
            
            for key, value in mix_data.items():
                if key.startswith('ingredient_') and value:
                    ingredient_key = key.replace('ingredient_', '')
                    percentage = float(value)
                    if percentage > 0:
                        feed_mix.add_ingredient(ingredient_key, percentage)
            
            # تطبيع النسب
            feed_mix.normalize_percentages()
            
            # حفظ التحديثات
            if db_manager.update_feed_mix(feed_mix):
                flash('تم تحديث خلطة العلف بنجاح', 'success')
                return redirect(url_for('feed_management'))
            else:
                flash('فشل في تحديث خلطة العلف', 'error')
        
        except Exception as e:
            flash(f'خطأ في تحديث خلطة العلف: {str(e)}', 'error')
    
    return render_template('edit_feed_mix.html', 
                         feed_mix=feed_mix, 
                         feed_ingredients=FEED_INGREDIENTS)

@app.route('/milk')
def milk_tracking():
    """صفحة تتبع الحليب"""
    try:
        # الحصول على التاريخ من المعاملات
        target_date_str = request.args.get('date', date.today().isoformat())
        target_date = datetime.strptime(target_date_str, '%Y-%m-%d').date()
        
        # سجلات الحليب لليوم المحدد
        milk_records = db_manager.get_milk_records_by_date(target_date)
        
        # ملخص اليوم
        daily_summary = db_manager.get_daily_milk_summary(target_date)
        
        # الأبقار النشطة
        active_cows = db_manager.get_all_cows(active_only=True)
        
        return render_template('milk_tracking.html',
                             milk_records=milk_records,
                             daily_summary=daily_summary,
                             active_cows=active_cows,
                             target_date=target_date)
    
    except Exception as e:
        flash(f'خطأ في تحميل بيانات الحليب: {str(e)}', 'error')
        return render_template('milk_tracking.html',
                             milk_records=[],
                             daily_summary={},
                             active_cows=[],
                             target_date=date.today())

@app.route('/milk/add', methods=['POST'])
def add_milk_record():
    """إضافة سجل حليب"""
    try:
        record_data = request.form.to_dict()

        cow_id = record_data['cow_id']
        record_date = datetime.strptime(record_data['record_date'], '%Y-%m-%d').date()
        morning_milk = float(record_data.get('morning_milk', 0))
        evening_milk = float(record_data.get('evening_milk', 0))
        notes = record_data.get('notes', '')

        if db_manager.add_milk_record(cow_id, record_date, morning_milk, evening_milk, notes):
            flash('تم إضافة سجل الحليب بنجاح', 'success')
        else:
            flash('فشل في إضافة سجل الحليب', 'error')

    except Exception as e:
        flash(f'خطأ في إضافة سجل الحليب: {str(e)}', 'error')

    return redirect(url_for('milk_tracking'))

@app.route('/milk/monthly')
def monthly_milk_report():
    """صفحة تقرير الحليب الشهري"""
    try:
        # الحصول على الشهر والسنة من المعاملات
        year = int(request.args.get('year', date.today().year))
        month = int(request.args.get('month', date.today().month))

        # حساب أول وآخر يوم في الشهر
        first_day = date(year, month, 1)
        if month == 12:
            last_day = date(year + 1, 1, 1) - timedelta(days=1)
        else:
            last_day = date(year, month + 1, 1) - timedelta(days=1)

        # الحصول على جميع سجلات الحليب للشهر
        monthly_records = db_manager.get_milk_records_by_date_range(first_day, last_day)

        # الحصول على الأبقار النشطة
        active_cows = db_manager.get_all_cows(active_only=True)

        # حساب الإحصائيات الشهرية
        monthly_stats = calculate_monthly_statistics(monthly_records, first_day, last_day)

        # إعداد بيانات الرسم البياني
        chart_data = prepare_monthly_chart_data(monthly_records, first_day, last_day)

        return render_template('monthly_milk_report.html',
                             monthly_records=monthly_records,
                             monthly_stats=monthly_stats,
                             chart_data=chart_data,
                             active_cows=active_cows,
                             current_year=year,
                             current_month=month,
                             first_day=first_day,
                             last_day=last_day)

    except Exception as e:
        flash(f'خطأ في تحميل التقرير الشهري: {str(e)}', 'error')
        return render_template('monthly_milk_report.html',
                             monthly_records=[],
                             monthly_stats={},
                             chart_data={},
                             active_cows=[],
                             current_year=date.today().year,
                             current_month=date.today().month,
                             first_day=date.today(),
                             last_day=date.today())

@app.route('/milk/cow/<cow_id>')
def cow_monthly_report(cow_id):
    """تقرير الحليب الشهري لبقرة واحدة"""
    try:
        # الحصول على الشهر والسنة من المعاملات
        year = int(request.args.get('year', date.today().year))
        month = int(request.args.get('month', date.today().month))

        # حساب أول وآخر يوم في الشهر
        first_day = date(year, month, 1)
        if month == 12:
            last_day = date(year + 1, 1, 1) - timedelta(days=1)
        else:
            last_day = date(year, month + 1, 1) - timedelta(days=1)

        # الحصول على بيانات البقرة
        cow = db_manager.get_cow(cow_id)
        if not cow:
            flash('البقرة غير موجودة', 'error')
            return redirect(url_for('monthly_milk_report'))

        # الحصول على سجلات الحليب للبقرة في الشهر المحدد
        cow_records = db_manager.get_cow_milk_records_by_month(cow_id, first_day, last_day)

        # حساب الإحصائيات للبقرة
        cow_stats = calculate_cow_monthly_statistics(cow_records, first_day, last_day)

        # إعداد بيانات الرسم البياني
        chart_data = prepare_cow_monthly_chart_data(cow_records, first_day, last_day)

        return render_template('cow_monthly_report.html',
                             cow=cow,
                             cow_records=cow_records,
                             cow_stats=cow_stats,
                             chart_data=chart_data,
                             current_year=year,
                             current_month=month,
                             first_day=first_day,
                             last_day=last_day)

    except Exception as e:
        flash(f'خطأ في تحميل تقرير البقرة: {str(e)}', 'error')
        return redirect(url_for('monthly_milk_report'))

@app.route('/reports')
def reports_dashboard():
    """صفحة التقارير"""
    try:
        # إحصائيات عامة
        herd_stats = db_manager.get_herd_statistics()
        
        # إنتاج الأسبوع الماضي
        end_date = date.today()
        start_date = end_date - timedelta(days=7)
        
        weekly_data = []
        for i in range(7):
            check_date = start_date + timedelta(days=i)
            daily_summary = db_manager.get_daily_milk_summary(check_date)
            weekly_data.append({
                'date': check_date,
                'total_milk': daily_summary.get('total_milk', 0)
            })
        
        return render_template('reports_dashboard.html',
                             herd_stats=herd_stats,
                             weekly_data=weekly_data)
    
    except Exception as e:
        flash(f'خطأ في تحميل التقارير: {str(e)}', 'error')
        return render_template('reports_dashboard.html',
                             herd_stats={},
                             weekly_data=[])

@app.route('/api/feed_mix/<mix_id>')
def get_feed_mix_details(mix_id):
    """API لجلب تفاصيل خلطة العلف"""
    try:
        feed_mix = db_manager.get_feed_mix(mix_id)
        if not feed_mix:
            return jsonify({'error': 'خلطة العلف غير موجودة'}), 404

        # تحويل البيانات إلى قاموس مع معلومات إضافية
        mix_data = feed_mix.to_dict()

        # إضافة معلومات المكونات مع التفاصيل
        ingredients_details = []
        for ingredient_key, percentage in feed_mix.ingredients.items():
            if ingredient_key in FEED_INGREDIENTS:
                ingredient_info = FEED_INGREDIENTS[ingredient_key].copy()
                ingredient_info['percentage'] = percentage
                ingredient_info['key'] = ingredient_key
                ingredients_details.append(ingredient_info)

        mix_data['ingredients_details'] = ingredients_details

        return jsonify(mix_data)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/feed_mix/<mix_id>/analysis')
def get_feed_mix_analysis(mix_id):
    """API لتحليل خلطة العلف"""
    try:
        feed_mix = db_manager.get_feed_mix(mix_id)
        if not feed_mix:
            return jsonify({'error': 'خلطة العلف غير موجودة'}), 404

        # تحليل غذائي شامل
        analysis = nutrition_analyzer.analyze_feed_mix(feed_mix)

        # تحليل اقتصادي
        economic_analysis = {
            'cost_per_kg': feed_mix.cost_per_kg,
            'cost_per_ton': feed_mix.cost_per_ton,
            'cost_effectiveness': feed_mix.quality_score / feed_mix.cost_per_kg if feed_mix.cost_per_kg > 0 else 0
        }

        return jsonify({
            'nutritional_analysis': analysis,
            'economic_analysis': economic_analysis,
            'quality_score': feed_mix.quality_score,
            'warnings': feed_mix.warnings,
            'recommendations': feed_mix.recommendations
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/nutrition_analysis/<cow_id>/<mix_id>')
def nutrition_analysis_api(cow_id, mix_id):
    """API لتحليل التغذية"""
    try:
        cow = db_manager.get_cow(cow_id)
        feed_mix = db_manager.get_feed_mix(mix_id)

        if not cow or not feed_mix:
            return jsonify({'error': 'البقرة أو خلطة العلف غير موجودة'}), 404

        analysis = nutrition_analyzer.analyze_feed_adequacy(cow, feed_mix)
        recommendations = nutrition_analyzer.generate_feeding_recommendations(cow, feed_mix)
        economic_analysis = nutrition_analyzer.calculate_economic_efficiency(cow, feed_mix)

        return jsonify({
            'analysis': analysis,
            'recommendations': recommendations,
            'economic_analysis': economic_analysis
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/generate_report/<report_type>')
def generate_report_api(report_type):
    """API لإنشاء التقارير"""
    try:
        if report_type == 'daily':
            target_date = datetime.strptime(request.args.get('date', date.today().isoformat()), '%Y-%m-%d').date()
            milk_records = db_manager.get_milk_records_by_date(target_date)
            report_path = report_generator.generate_daily_milk_report(milk_records, target_date)
            
        elif report_type == 'herd':
            herd_stats = db_manager.get_herd_statistics()
            cows_data = [cow.to_dict() for cow in db_manager.get_all_cows()]
            report_path = report_generator.generate_herd_summary_report(herd_stats, cows_data)
            
        else:
            return jsonify({'error': 'نوع التقرير غير مدعوم'}), 400
        
        return send_file(report_path, as_attachment=True)
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.errorhandler(404)
def not_found_error(error):
    """صفحة الخطأ 404"""
    return render_template('errors/404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    """صفحة الخطأ 500"""
    return render_template('errors/500.html'), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
