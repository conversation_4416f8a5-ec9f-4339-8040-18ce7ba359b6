#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نقطة البداية الرئيسية لنظام إدارة مزرعة الأبقار الحلوب
Main entry point for Dairy Farm Management System
"""

import os
import sys
from datetime import date, datetime
import webbrowser
import threading
import time

def create_sample_data():
    """إنشاء بيانات تجريبية للنظام"""
    from database.db_manager import DatabaseManager
    from models.cow import Cow
    from models.feed_mix import FeedMix
    
    db_manager = DatabaseManager()
    
    # إنشاء أبقار تجريبية
    sample_cows = [
        {
            'cow_id': 'C001',
            'name': 'فاطمة',
            'breed': 'holstein',
            'birth_date': date(2021, 3, 15),
            'weight': 650.0,
            'last_calving_date': date(2023, 8, 10),
            'health_status': 'سليمة',
            'lactation_number': 2
        },
        {
            'cow_id': 'C002',
            'name': 'عائشة',
            'breed': 'friesian',
            'birth_date': date(2020, 7, 22),
            'weight': 620.0,
            'last_calving_date': date(2023, 9, 5),
            'health_status': 'سليمة',
            'lactation_number': 3
        },
        {
            'cow_id': 'C003',
            'name': 'خديجة',
            'breed': 'jersey',
            'birth_date': date(2022, 1, 8),
            'weight': 450.0,
            'last_calving_date': date(2023, 10, 20),
            'health_status': 'حامل',
            'lactation_number': 1
        },
        {
            'cow_id': 'C004',
            'name': 'زينب',
            'breed': 'brown_swiss',
            'birth_date': date(2021, 11, 30),
            'weight': 600.0,
            'last_calving_date': date(2023, 7, 15),
            'health_status': 'سليمة',
            'lactation_number': 2
        },
        {
            'cow_id': 'C005',
            'name': 'مريم',
            'breed': 'local',
            'birth_date': date(2020, 5, 12),
            'weight': 400.0,
            'last_calving_date': date(2023, 6, 25),
            'health_status': 'جافة',
            'lactation_number': 3
        }
    ]
    
    print("إنشاء الأبقار التجريبية...")
    for cow_data in sample_cows:
        cow = Cow(
            cow_id=cow_data['cow_id'],
            name=cow_data['name'],
            breed=cow_data['breed'],
            birth_date=cow_data['birth_date'],
            weight=cow_data['weight']
        )
        
        cow.last_calving_date = cow_data['last_calving_date']
        cow.health_status = cow_data['health_status']
        cow.lactation_number = cow_data['lactation_number']
        
        # إضافة سجلات حليب تجريبية
        if cow.health_status == 'سليمة':
            import random
            for i in range(7):  # آخر 7 أيام
                record_date = date.today() - datetime.timedelta(days=i)
                morning_milk = random.uniform(12, 18)
                evening_milk = random.uniform(10, 16)
                cow.add_milk_record(record_date, morning_milk, evening_milk)
        
        try:
            db_manager.add_cow(cow)
            print(f"✓ تم إضافة البقرة {cow.name} ({cow.cow_id})")
        except:
            print(f"✗ فشل في إضافة البقرة {cow.name} - قد تكون موجودة مسبقاً")
    
    # إنشاء خلطات علف تجريبية
    sample_mixes = [
        {
            'mix_id': 'MIX001',
            'name': 'خلطة الإنتاج العالي',
            'description': 'خلطة مخصصة للأبقار عالية الإنتاج',
            'ingredients': {
                'corn': 35.0,
                'soybean_meal': 20.0,
                'alfalfa_hay': 25.0,
                'wheat_bran': 15.0,
                'limestone': 3.0,
                'salt': 2.0
            }
        },
        {
            'mix_id': 'MIX002',
            'name': 'خلطة الصيانة',
            'description': 'خلطة للأبقار الجافة والصيانة',
            'ingredients': {
                'barley': 30.0,
                'alfalfa_hay': 40.0,
                'wheat_bran': 20.0,
                'rice_bran': 8.0,
                'limestone': 1.5,
                'salt': 0.5
            }
        },
        {
            'mix_id': 'MIX003',
            'name': 'خلطة اقتصادية',
            'description': 'خلطة منخفضة التكلفة للاستخدام العام',
            'ingredients': {
                'corn': 25.0,
                'barley': 25.0,
                'alfalfa_hay': 30.0,
                'rice_bran': 15.0,
                'cotton_seed_meal': 3.0,
                'limestone': 1.5,
                'salt': 0.5
            }
        }
    ]
    
    print("\nإنشاء خلطات العلف التجريبية...")
    for mix_data in sample_mixes:
        feed_mix = FeedMix(
            mix_id=mix_data['mix_id'],
            name=mix_data['name'],
            description=mix_data['description']
        )
        
        for ingredient, percentage in mix_data['ingredients'].items():
            feed_mix.add_ingredient(ingredient, percentage)
        
        try:
            db_manager.add_feed_mix(feed_mix)
            print(f"✓ تم إضافة خلطة العلف {feed_mix.name} ({feed_mix.mix_id})")
        except:
            print(f"✗ فشل في إضافة خلطة العلف {feed_mix.name} - قد تكون موجودة مسبقاً")
    
    print("\n✅ تم إنشاء البيانات التجريبية بنجاح!")

def open_browser():
    """فتح المتصفح بعد تأخير قصير"""
    time.sleep(2)  # انتظار حتى يبدأ الخادم
    webbrowser.open('http://localhost:5000')

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🐄 نظام إدارة مزرعة الأبقار الحلوب")
    print("   Dairy Farm Management System")
    print("=" * 60)
    
    # التحقق من وجود قاعدة البيانات
    if not os.path.exists('dairy_farm.db'):
        print("\n📊 إنشاء قاعدة البيانات لأول مرة...")
        from database.db_manager import DatabaseManager
        db_manager = DatabaseManager()
        
        # سؤال المستخدم عن إنشاء بيانات تجريبية
        response = input("\n❓ هل تريد إنشاء بيانات تجريبية؟ (y/n): ").lower().strip()
        if response in ['y', 'yes', 'نعم', 'ن']:
            create_sample_data()
    
    print("\n🚀 بدء تشغيل الخادم...")
    print("📱 سيتم فتح التطبيق في المتصفح تلقائياً")
    print("🌐 أو يمكنك زيارة: http://localhost:5000")
    print("\n⚠️  للإيقاف: اضغط Ctrl+C")
    print("-" * 60)
    
    # فتح المتصفح في خيط منفصل
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    # تشغيل التطبيق
    try:
        from app import app
        app.run(debug=False, host='0.0.0.0', port=5000, use_reloader=False)
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف النظام بنجاح")
        print("شكراً لاستخدام نظام إدارة مزرعة الأبقار!")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {e}")
        print("تأكد من تثبيت جميع المتطلبات: pip install -r requirements.txt")

if __name__ == '__main__':
    main()
