{% extends "base.html" %}

{% block title %}إضافة خلطة علف جديدة - نظام إدارة مزرعة الأبقار{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h2">
        <i class="fas fa-plus-circle text-primary"></i>
        إضافة خلطة علف جديدة
    </h1>
    <a href="{{ url_for('feed_management') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-right"></i>
        العودة للقائمة
    </a>
</div>

<div class="row justify-content-center">
    <div class="col-lg-10">
        <form method="POST" class="needs-validation" novalidate>
            <!-- المعلومات الأساسية -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle"></i>
                        المعلومات الأساسية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="mix_id" class="form-label">رقم الخلطة <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="mix_id" name="mix_id" required
                                   placeholder="مثال: MIX001">
                            <div class="invalid-feedback">
                                يرجى إدخال رقم الخلطة
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">اسم الخلطة <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" required
                                   placeholder="مثال: خلطة الإنتاج العالي">
                            <div class="invalid-feedback">
                                يرجى إدخال اسم الخلطة
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">وصف الخلطة</label>
                        <textarea class="form-control" id="description" name="description" rows="3"
                                  placeholder="وصف مختصر لخلطة العلف واستخداماتها..."></textarea>
                    </div>
                </div>
            </div>

            <!-- مكونات الخلطة -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-seedling"></i>
                        مكونات الخلطة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for ingredient_key, ingredient_data in feed_ingredients.items() %}
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h6 class="card-title">{{ ingredient_data.name_ar }}</h6>
                                    <p class="card-text small text-muted">{{ ingredient_data.name_en }}</p>
                                    
                                    <div class="mb-2">
                                        <label for="ingredient_{{ ingredient_key }}" class="form-label">النسبة المئوية</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" 
                                                   id="ingredient_{{ ingredient_key }}" 
                                                   name="ingredient_{{ ingredient_key }}"
                                                   min="0" max="100" step="0.1" value="0">
                                            <span class="input-group-text">%</span>
                                        </div>
                                    </div>
                                    
                                    <div class="small text-muted">
                                        <div>بروتين: {{ ingredient_data.crude_protein }}%</div>
                                        <div>طاقة: {{ ingredient_data.energy_mcal }} ميجا كالوري/كغ</div>
                                        <div>سعر: {{ ingredient_data.price_per_kg }} د.أ/كغ</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    
                    <div class="alert alert-info mt-3">
                        <i class="fas fa-info-circle"></i>
                        <strong>ملاحظة:</strong> مجموع النسب المئوية يجب أن يكون 100%. سيتم تطبيع النسب تلقائياً عند الحفظ.
                    </div>
                </div>
            </div>

            <!-- ملخص الخلطة -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calculator"></i>
                        ملخص الخلطة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3 mb-3">
                            <h5 id="totalPercentage" class="text-primary">0%</h5>
                            <small class="text-muted">إجمالي النسب</small>
                        </div>
                        <div class="col-md-3 mb-3">
                            <h5 id="estimatedProtein" class="text-success">0%</h5>
                            <small class="text-muted">البروتين المتوقع</small>
                        </div>
                        <div class="col-md-3 mb-3">
                            <h5 id="estimatedEnergy" class="text-info">0</h5>
                            <small class="text-muted">الطاقة المتوقعة (ميجا كالوري/كغ)</small>
                        </div>
                        <div class="col-md-3 mb-3">
                            <h5 id="estimatedCost" class="text-warning">0</h5>
                            <small class="text-muted">التكلفة المتوقعة (د.أ/كغ)</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أزرار الحفظ -->
            <div class="card">
                <div class="card-body">
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('feed_management') }}" class="btn btn-outline-secondary me-md-2">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ الخلطة
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// بيانات المكونات
const ingredients = {{ feed_ingredients|tojson }};

// تحديث الملخص عند تغيير النسب
function updateSummary() {
    let totalPercentage = 0;
    let totalProtein = 0;
    let totalEnergy = 0;
    let totalCost = 0;
    
    Object.keys(ingredients).forEach(key => {
        const input = document.getElementById(`ingredient_${key}`);
        const percentage = parseFloat(input.value) || 0;
        const ingredient = ingredients[key];
        
        totalPercentage += percentage;
        totalProtein += (ingredient.crude_protein * percentage / 100);
        totalEnergy += (ingredient.energy_mcal * percentage / 100);
        totalCost += (ingredient.price_per_kg * percentage / 100);
    });
    
    document.getElementById('totalPercentage').textContent = totalPercentage.toFixed(1) + '%';
    document.getElementById('estimatedProtein').textContent = totalProtein.toFixed(1) + '%';
    document.getElementById('estimatedEnergy').textContent = totalEnergy.toFixed(2);
    document.getElementById('estimatedCost').textContent = totalCost.toFixed(3);
    
    // تغيير لون إجمالي النسب
    const totalElement = document.getElementById('totalPercentage');
    if (totalPercentage === 100) {
        totalElement.className = 'text-success';
    } else if (totalPercentage > 100) {
        totalElement.className = 'text-danger';
    } else {
        totalElement.className = 'text-warning';
    }
}

// ربط الأحداث
document.addEventListener('DOMContentLoaded', function() {
    Object.keys(ingredients).forEach(key => {
        const input = document.getElementById(`ingredient_${key}`);
        input.addEventListener('input', updateSummary);
    });
    
    updateSummary();
});

// التحقق من صحة النموذج
(function() {
    'use strict';
    window.addEventListener('load', function() {
        const forms = document.getElementsByClassName('needs-validation');
        const validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                const totalPercentage = parseFloat(document.getElementById('totalPercentage').textContent);
                
                if (form.checkValidity() === false || totalPercentage === 0) {
                    event.preventDefault();
                    event.stopPropagation();
                    
                    if (totalPercentage === 0) {
                        alert('يرجى إدخال نسب المكونات');
                    }
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
{% endblock %}
