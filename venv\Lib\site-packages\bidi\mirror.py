# This file is part of python-bidi
#
# python-bidi is free software: you can redistribute it and/or modify
# it under the terms of the GNU Lesser General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU Lesser General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.

# Copyright (C) 2008-2010 <PERSON><PERSON><PERSON> <kzamir_a_walla.co.il>,
# Copyright (C) 2010-2015 <PERSON><PERSON> krihel<PERSON> <<EMAIL>>.
"""Mirrored chars"""

# Can't seem to get this data from python's unicode data, so this is imported
# from http://www.unicode.org/Public/UNIDATA/BidiMirroring.txt
MIRRORED = {
    u'\u0028': u'\u0029',  # LEFT PARENTHESIS
    u'\u0029': u'\u0028',  # RIGHT PARENTHESIS
    u'\u003C': u'\u003E',  # LESS-THAN SIGN
    u'\u003E': u'\u003C',  # GREATER-THAN SIGN
    u'\u005B': u'\u005D',  # LEFT SQUARE BRACKET
    u'\u005D': u'\u005B',  # RIGHT SQUARE BRACKET
    u'\u007B': u'\u007D',  # LEFT CURLY BRACKET
    u'\u007D': u'\u007B',  # RIGHT CURLY BRACKET
    u'\u00AB': u'\u00BB',  # LEFT-POINTING DOUBLE ANGLE QUOTATION MARK
    u'\u00BB': u'\u00AB',  # RIGHT-POINTING DOUBLE ANGLE QUOTATION MARK
    u'\u0F3A': u'\u0F3B',  # TIBETAN MARK GUG RTAGS GYON
    u'\u0F3B': u'\u0F3A',  # TIBETAN MARK GUG RTAGS GYAS
    u'\u0F3C': u'\u0F3D',  # TIBETAN MARK ANG KHANG GYON
    u'\u0F3D': u'\u0F3C',  # TIBETAN MARK ANG KHANG GYAS
    u'\u169B': u'\u169C',  # OGHAM FEATHER MARK
    u'\u169C': u'\u169B',  # OGHAM REVERSED FEATHER MARK
    u'\u2039': u'\u203A',  # SINGLE LEFT-POINTING ANGLE QUOTATION MARK
    u'\u203A': u'\u2039',  # SINGLE RIGHT-POINTING ANGLE QUOTATION MARK
    u'\u2045': u'\u2046',  # LEFT SQUARE BRACKET WITH QUILL
    u'\u2046': u'\u2045',  # RIGHT SQUARE BRACKET WITH QUILL
    u'\u207D': u'\u207E',  # SUPERSCRIPT LEFT PARENTHESIS
    u'\u207E': u'\u207D',  # SUPERSCRIPT RIGHT PARENTHESIS
    u'\u208D': u'\u208E',  # SUBSCRIPT LEFT PARENTHESIS
    u'\u208E': u'\u208D',  # SUBSCRIPT RIGHT PARENTHESIS
    u'\u2208': u'\u220B',  # ELEMENT OF
    u'\u2209': u'\u220C',  # NOT AN ELEMENT OF
    u'\u220A': u'\u220D',  # SMALL ELEMENT OF
    u'\u220B': u'\u2208',  # CONTAINS AS MEMBER
    u'\u220C': u'\u2209',  # DOES NOT CONTAIN AS MEMBER
    u'\u220D': u'\u220A',  # SMALL CONTAINS AS MEMBER
    u'\u2215': u'\u29F5',  # DIVISION SLASH
    u'\u223C': u'\u223D',  # TILDE OPERATOR
    u'\u223D': u'\u223C',  # REVERSED TILDE
    u'\u2243': u'\u22CD',  # ASYMPTOTICALLY EQUAL TO
    u'\u2252': u'\u2253',  # APPROXIMATELY EQUAL TO OR THE IMAGE OF
    u'\u2253': u'\u2252',  # IMAGE OF OR APPROXIMATELY EQUAL TO
    u'\u2254': u'\u2255',  # COLON EQUALS
    u'\u2255': u'\u2254',  # EQUALS COLON
    u'\u2264': u'\u2265',  # LESS-THAN OR EQUAL TO
    u'\u2265': u'\u2264',  # GREATER-THAN OR EQUAL TO
    u'\u2266': u'\u2267',  # LESS-THAN OVER EQUAL TO
    u'\u2267': u'\u2266',  # GREATER-THAN OVER EQUAL TO
    u'\u2268': u'\u2269',  # [BEST FIT] LESS-THAN BUT NOT EQUAL TO
    u'\u2269': u'\u2268',  # [BEST FIT] GREATER-THAN BUT NOT EQUAL TO
    u'\u226A': u'\u226B',  # MUCH LESS-THAN
    u'\u226B': u'\u226A',  # MUCH GREATER-THAN
    u'\u226E': u'\u226F',  # [BEST FIT] NOT LESS-THAN
    u'\u226F': u'\u226E',  # [BEST FIT] NOT GREATER-THAN
    u'\u2270': u'\u2271',  # [BEST FIT] NEITHER LESS-THAN NOR EQUAL TO
    u'\u2271': u'\u2270',  # [BEST FIT] NEITHER GREATER-THAN NOR EQUAL TO
    u'\u2272': u'\u2273',  # [BEST FIT] LESS-THAN OR EQUIVALENT TO
    u'\u2273': u'\u2272',  # [BEST FIT] GREATER-THAN OR EQUIVALENT TO
    u'\u2274': u'\u2275',  # [BEST FIT] NEITHER LESS-THAN NOR EQUIVALENT TO
    u'\u2275': u'\u2274',  # [BEST FIT] NEITHER GREATER-THAN NOR EQUIVALENT TO
    u'\u2276': u'\u2277',  # LESS-THAN OR GREATER-THAN
    u'\u2277': u'\u2276',  # GREATER-THAN OR LESS-THAN
    u'\u2278': u'\u2279',  # [BEST FIT] NEITHER LESS-THAN NOR GREATER-THAN
    u'\u2279': u'\u2278',  # [BEST FIT] NEITHER GREATER-THAN NOR LESS-THAN
    u'\u227A': u'\u227B',  # PRECEDES
    u'\u227B': u'\u227A',  # SUCCEEDS
    u'\u227C': u'\u227D',  # PRECEDES OR EQUAL TO
    u'\u227D': u'\u227C',  # SUCCEEDS OR EQUAL TO
    u'\u227E': u'\u227F',  # [BEST FIT] PRECEDES OR EQUIVALENT TO
    u'\u227F': u'\u227E',  # [BEST FIT] SUCCEEDS OR EQUIVALENT TO
    u'\u2280': u'\u2281',  # [BEST FIT] DOES NOT PRECEDE
    u'\u2281': u'\u2280',  # [BEST FIT] DOES NOT SUCCEED
    u'\u2282': u'\u2283',  # SUBSET OF
    u'\u2283': u'\u2282',  # SUPERSET OF
    u'\u2284': u'\u2285',  # [BEST FIT] NOT A SUBSET OF
    u'\u2285': u'\u2284',  # [BEST FIT] NOT A SUPERSET OF
    u'\u2286': u'\u2287',  # SUBSET OF OR EQUAL TO
    u'\u2287': u'\u2286',  # SUPERSET OF OR EQUAL TO
    u'\u2288': u'\u2289',  # [BEST FIT] NEITHER A SUBSET OF NOR EQUAL TO
    u'\u2289': u'\u2288',  # [BEST FIT] NEITHER A SUPERSET OF NOR EQUAL TO
    u'\u228A': u'\u228B',  # [BEST FIT] SUBSET OF WITH NOT EQUAL TO
    u'\u228B': u'\u228A',  # [BEST FIT] SUPERSET OF WITH NOT EQUAL TO
    u'\u228F': u'\u2290',  # SQUARE IMAGE OF
    u'\u2290': u'\u228F',  # SQUARE ORIGINAL OF
    u'\u2291': u'\u2292',  # SQUARE IMAGE OF OR EQUAL TO
    u'\u2292': u'\u2291',  # SQUARE ORIGINAL OF OR EQUAL TO
    u'\u2298': u'\u29B8',  # CIRCLED DIVISION SLASH
    u'\u22A2': u'\u22A3',  # RIGHT TACK
    u'\u22A3': u'\u22A2',  # LEFT TACK
    u'\u22A6': u'\u2ADE',  # ASSERTION
    u'\u22A8': u'\u2AE4',  # TRUE
    u'\u22A9': u'\u2AE3',  # FORCES
    u'\u22AB': u'\u2AE5',  # DOUBLE VERTICAL BAR DOUBLE RIGHT TURNSTILE
    u'\u22B0': u'\u22B1',  # PRECEDES UNDER RELATION
    u'\u22B1': u'\u22B0',  # SUCCEEDS UNDER RELATION
    u'\u22B2': u'\u22B3',  # NORMAL SUBGROUP OF
    u'\u22B3': u'\u22B2',  # CONTAINS AS NORMAL SUBGROUP
    u'\u22B4': u'\u22B5',  # NORMAL SUBGROUP OF OR EQUAL TO
    u'\u22B5': u'\u22B4',  # CONTAINS AS NORMAL SUBGROUP OR EQUAL TO
    u'\u22B6': u'\u22B7',  # ORIGINAL OF
    u'\u22B7': u'\u22B6',  # IMAGE OF
    u'\u22C9': u'\u22CA',  # LEFT NORMAL FACTOR SEMIDIRECT PRODUCT
    u'\u22CA': u'\u22C9',  # RIGHT NORMAL FACTOR SEMIDIRECT PRODUCT
    u'\u22CB': u'\u22CC',  # LEFT SEMIDIRECT PRODUCT
    u'\u22CC': u'\u22CB',  # RIGHT SEMIDIRECT PRODUCT
    u'\u22CD': u'\u2243',  # REVERSED TILDE EQUALS
    u'\u22D0': u'\u22D1',  # DOUBLE SUBSET
    u'\u22D1': u'\u22D0',  # DOUBLE SUPERSET
    u'\u22D6': u'\u22D7',  # LESS-THAN WITH DOT
    u'\u22D7': u'\u22D6',  # GREATER-THAN WITH DOT
    u'\u22D8': u'\u22D9',  # VERY MUCH LESS-THAN
    u'\u22D9': u'\u22D8',  # VERY MUCH GREATER-THAN
    u'\u22DA': u'\u22DB',  # LESS-THAN EQUAL TO OR GREATER-THAN
    u'\u22DB': u'\u22DA',  # GREATER-THAN EQUAL TO OR LESS-THAN
    u'\u22DC': u'\u22DD',  # EQUAL TO OR LESS-THAN
    u'\u22DD': u'\u22DC',  # EQUAL TO OR GREATER-THAN
    u'\u22DE': u'\u22DF',  # EQUAL TO OR PRECEDES
    u'\u22DF': u'\u22DE',  # EQUAL TO OR SUCCEEDS
    u'\u22E0': u'\u22E1',  # [BEST FIT] DOES NOT PRECEDE OR EQUAL
    u'\u22E1': u'\u22E0',  # [BEST FIT] DOES NOT SUCCEED OR EQUAL
    u'\u22E2': u'\u22E3',  # [BEST FIT] NOT SQUARE IMAGE OF OR EQUAL TO
    u'\u22E3': u'\u22E2',  # [BEST FIT] NOT SQUARE ORIGINAL OF OR EQUAL TO
    u'\u22E4': u'\u22E5',  # [BEST FIT] SQUARE IMAGE OF OR NOT EQUAL TO
    u'\u22E5': u'\u22E4',  # [BEST FIT] SQUARE ORIGINAL OF OR NOT EQUAL TO
    u'\u22E6': u'\u22E7',  # [BEST FIT] LESS-THAN BUT NOT EQUIVALENT TO
    u'\u22E7': u'\u22E6',  # [BEST FIT] GREATER-THAN BUT NOT EQUIVALENT TO
    u'\u22E8': u'\u22E9',  # [BEST FIT] PRECEDES BUT NOT EQUIVALENT TO
    u'\u22E9': u'\u22E8',  # [BEST FIT] SUCCEEDS BUT NOT EQUIVALENT TO
    u'\u22EA': u'\u22EB',  # [BEST FIT] NOT NORMAL SUBGROUP OF
    u'\u22EB': u'\u22EA',  # [BEST FIT] DOES NOT CONTAIN AS NORMAL SUBGROUP
    u'\u22EC': u'\u22ED',  # [BEST FIT] NOT NORMAL SUBGROUP OF OR EQUAL TO
    # [BEST FIT] DOES NOT CONTAIN AS NORMAL SUBGROUP OR EQUAL
    u'\u22ED': u'\u22EC',
    u'\u22F0': u'\u22F1',  # UP RIGHT DIAGONAL ELLIPSIS
    u'\u22F1': u'\u22F0',  # DOWN RIGHT DIAGONAL ELLIPSIS
    u'\u22F2': u'\u22FA',  # ELEMENT OF WITH LONG HORIZONTAL STROKE
    u'\u22F3': u'\u22FB',  # ELEMENT OF WITH VERTICAL BAR AT END OF HORIZONTAL STROKE
    u'\u22F4': u'\u22FC',  # SMALL ELEMENT OF WITH VERTICAL BAR AT END OF HORIZONTAL STROKE
    u'\u22F6': u'\u22FD',  # ELEMENT OF WITH OVERBAR
    u'\u22F7': u'\u22FE',  # SMALL ELEMENT OF WITH OVERBAR
    u'\u22FA': u'\u22F2',  # CONTAINS WITH LONG HORIZONTAL STROKE
    u'\u22FB': u'\u22F3',  # CONTAINS WITH VERTICAL BAR AT END OF HORIZONTAL STROKE
    u'\u22FC': u'\u22F4',  # SMALL CONTAINS WITH VERTICAL BAR AT END OF HORIZONTAL STROKE
    u'\u22FD': u'\u22F6',  # CONTAINS WITH OVERBAR
    u'\u22FE': u'\u22F7',  # SMALL CONTAINS WITH OVERBAR
    u'\u2308': u'\u2309',  # LEFT CEILING
    u'\u2309': u'\u2308',  # RIGHT CEILING
    u'\u230A': u'\u230B',  # LEFT FLOOR
    u'\u230B': u'\u230A',  # RIGHT FLOOR
    u'\u2329': u'\u232A',  # LEFT-POINTING ANGLE BRACKET
    u'\u232A': u'\u2329',  # RIGHT-POINTING ANGLE BRACKET
    u'\u2768': u'\u2769',  # MEDIUM LEFT PARENTHESIS ORNAMENT
    u'\u2769': u'\u2768',  # MEDIUM RIGHT PARENTHESIS ORNAMENT
    u'\u276A': u'\u276B',  # MEDIUM FLATTENED LEFT PARENTHESIS ORNAMENT
    u'\u276B': u'\u276A',  # MEDIUM FLATTENED RIGHT PARENTHESIS ORNAMENT
    u'\u276C': u'\u276D',  # MEDIUM LEFT-POINTING ANGLE BRACKET ORNAMENT
    u'\u276D': u'\u276C',  # MEDIUM RIGHT-POINTING ANGLE BRACKET ORNAMENT
    u'\u276E': u'\u276F',  # HEAVY LEFT-POINTING ANGLE QUOTATION MARK ORNAMENT
    u'\u276F': u'\u276E',  # HEAVY RIGHT-POINTING ANGLE QUOTATION MARK ORNAMENT
    u'\u2770': u'\u2771',  # HEAVY LEFT-POINTING ANGLE BRACKET ORNAMENT
    u'\u2771': u'\u2770',  # HEAVY RIGHT-POINTING ANGLE BRACKET ORNAMENT
    u'\u2772': u'\u2773',  # LIGHT LEFT TORTOISE SHELL BRACKET
    u'\u2773': u'\u2772',  # LIGHT RIGHT TORTOISE SHELL BRACKET
    u'\u2774': u'\u2775',  # MEDIUM LEFT CURLY BRACKET ORNAMENT
    u'\u2775': u'\u2774',  # MEDIUM RIGHT CURLY BRACKET ORNAMENT
    u'\u27C3': u'\u27C4',  # OPEN SUBSET
    u'\u27C4': u'\u27C3',  # OPEN SUPERSET
    u'\u27C5': u'\u27C6',  # LEFT S-SHAPED BAG DELIMITER
    u'\u27C6': u'\u27C5',  # RIGHT S-SHAPED BAG DELIMITER
    u'\u27C8': u'\u27C9',  # REVERSE SOLIDUS PRECEDING SUBSET
    u'\u27C9': u'\u27C8',  # SUPERSET PRECEDING SOLIDUS
    u'\u27D5': u'\u27D6',  # LEFT OUTER JOIN
    u'\u27D6': u'\u27D5',  # RIGHT OUTER JOIN
    u'\u27DD': u'\u27DE',  # LONG RIGHT TACK
    u'\u27DE': u'\u27DD',  # LONG LEFT TACK
    u'\u27E2': u'\u27E3',  # WHITE CONCAVE-SIDED DIAMOND WITH LEFTWARDS TICK
    u'\u27E3': u'\u27E2',  # WHITE CONCAVE-SIDED DIAMOND WITH RIGHTWARDS TICK
    u'\u27E4': u'\u27E5',  # WHITE SQUARE WITH LEFTWARDS TICK
    u'\u27E5': u'\u27E4',  # WHITE SQUARE WITH RIGHTWARDS TICK
    u'\u27E6': u'\u27E7',  # MATHEMATICAL LEFT WHITE SQUARE BRACKET
    u'\u27E7': u'\u27E6',  # MATHEMATICAL RIGHT WHITE SQUARE BRACKET
    u'\u27E8': u'\u27E9',  # MATHEMATICAL LEFT ANGLE BRACKET
    u'\u27E9': u'\u27E8',  # MATHEMATICAL RIGHT ANGLE BRACKET
    u'\u27EA': u'\u27EB',  # MATHEMATICAL LEFT DOUBLE ANGLE BRACKET
    u'\u27EB': u'\u27EA',  # MATHEMATICAL RIGHT DOUBLE ANGLE BRACKET
    u'\u27EC': u'\u27ED',  # MATHEMATICAL LEFT WHITE TORTOISE SHELL BRACKET
    u'\u27ED': u'\u27EC',  # MATHEMATICAL RIGHT WHITE TORTOISE SHELL BRACKET
    u'\u27EE': u'\u27EF',  # MATHEMATICAL LEFT FLATTENED PARENTHESIS
    u'\u27EF': u'\u27EE',  # MATHEMATICAL RIGHT FLATTENED PARENTHESIS
    u'\u2983': u'\u2984',  # LEFT WHITE CURLY BRACKET
    u'\u2984': u'\u2983',  # RIGHT WHITE CURLY BRACKET
    u'\u2985': u'\u2986',  # LEFT WHITE PARENTHESIS
    u'\u2986': u'\u2985',  # RIGHT WHITE PARENTHESIS
    u'\u2987': u'\u2988',  # Z NOTATION LEFT IMAGE BRACKET
    u'\u2988': u'\u2987',  # Z NOTATION RIGHT IMAGE BRACKET
    u'\u2989': u'\u298A',  # Z NOTATION LEFT BINDING BRACKET
    u'\u298A': u'\u2989',  # Z NOTATION RIGHT BINDING BRACKET
    u'\u298B': u'\u298C',  # LEFT SQUARE BRACKET WITH UNDERBAR
    u'\u298C': u'\u298B',  # RIGHT SQUARE BRACKET WITH UNDERBAR
    u'\u298D': u'\u2990',  # LEFT SQUARE BRACKET WITH TICK IN TOP CORNER
    u'\u298E': u'\u298F',  # RIGHT SQUARE BRACKET WITH TICK IN BOTTOM CORNER
    u'\u298F': u'\u298E',  # LEFT SQUARE BRACKET WITH TICK IN BOTTOM CORNER
    u'\u2990': u'\u298D',  # RIGHT SQUARE BRACKET WITH TICK IN TOP CORNER
    u'\u2991': u'\u2992',  # LEFT ANGLE BRACKET WITH DOT
    u'\u2992': u'\u2991',  # RIGHT ANGLE BRACKET WITH DOT
    u'\u2993': u'\u2994',  # LEFT ARC LESS-THAN BRACKET
    u'\u2994': u'\u2993',  # RIGHT ARC GREATER-THAN BRACKET
    u'\u2995': u'\u2996',  # DOUBLE LEFT ARC GREATER-THAN BRACKET
    u'\u2996': u'\u2995',  # DOUBLE RIGHT ARC LESS-THAN BRACKET
    u'\u2997': u'\u2998',  # LEFT BLACK TORTOISE SHELL BRACKET
    u'\u2998': u'\u2997',  # RIGHT BLACK TORTOISE SHELL BRACKET
    u'\u29B8': u'\u2298',  # CIRCLED REVERSE SOLIDUS
    u'\u29C0': u'\u29C1',  # CIRCLED LESS-THAN
    u'\u29C1': u'\u29C0',  # CIRCLED GREATER-THAN
    u'\u29C4': u'\u29C5',  # SQUARED RISING DIAGONAL SLASH
    u'\u29C5': u'\u29C4',  # SQUARED FALLING DIAGONAL SLASH
    u'\u29CF': u'\u29D0',  # LEFT TRIANGLE BESIDE VERTICAL BAR
    u'\u29D0': u'\u29CF',  # VERTICAL BAR BESIDE RIGHT TRIANGLE
    u'\u29D1': u'\u29D2',  # BOWTIE WITH LEFT HALF BLACK
    u'\u29D2': u'\u29D1',  # BOWTIE WITH RIGHT HALF BLACK
    u'\u29D4': u'\u29D5',  # TIMES WITH LEFT HALF BLACK
    u'\u29D5': u'\u29D4',  # TIMES WITH RIGHT HALF BLACK
    u'\u29D8': u'\u29D9',  # LEFT WIGGLY FENCE
    u'\u29D9': u'\u29D8',  # RIGHT WIGGLY FENCE
    u'\u29DA': u'\u29DB',  # LEFT DOUBLE WIGGLY FENCE
    u'\u29DB': u'\u29DA',  # RIGHT DOUBLE WIGGLY FENCE
    u'\u29F5': u'\u2215',  # REVERSE SOLIDUS OPERATOR
    u'\u29F8': u'\u29F9',  # BIG SOLIDUS
    u'\u29F9': u'\u29F8',  # BIG REVERSE SOLIDUS
    u'\u29FC': u'\u29FD',  # LEFT-POINTING CURVED ANGLE BRACKET
    u'\u29FD': u'\u29FC',  # RIGHT-POINTING CURVED ANGLE BRACKET
    u'\u2A2B': u'\u2A2C',  # MINUS SIGN WITH FALLING DOTS
    u'\u2A2C': u'\u2A2B',  # MINUS SIGN WITH RISING DOTS
    u'\u2A2D': u'\u2A2E',  # PLUS SIGN IN LEFT HALF CIRCLE
    u'\u2A2E': u'\u2A2D',  # PLUS SIGN IN RIGHT HALF CIRCLE
    u'\u2A34': u'\u2A35',  # MULTIPLICATION SIGN IN LEFT HALF CIRCLE
    u'\u2A35': u'\u2A34',  # MULTIPLICATION SIGN IN RIGHT HALF CIRCLE
    u'\u2A3C': u'\u2A3D',  # INTERIOR PRODUCT
    u'\u2A3D': u'\u2A3C',  # RIGHTHAND INTERIOR PRODUCT
    u'\u2A64': u'\u2A65',  # Z NOTATION DOMAIN ANTIRESTRICTION
    u'\u2A65': u'\u2A64',  # Z NOTATION RANGE ANTIRESTRICTION
    u'\u2A79': u'\u2A7A',  # LESS-THAN WITH CIRCLE INSIDE
    u'\u2A7A': u'\u2A79',  # GREATER-THAN WITH CIRCLE INSIDE
    u'\u2A7D': u'\u2A7E',  # LESS-THAN OR SLANTED EQUAL TO
    u'\u2A7E': u'\u2A7D',  # GREATER-THAN OR SLANTED EQUAL TO
    u'\u2A7F': u'\u2A80',  # LESS-THAN OR SLANTED EQUAL TO WITH DOT INSIDE
    u'\u2A80': u'\u2A7F',  # GREATER-THAN OR SLANTED EQUAL TO WITH DOT INSIDE
    u'\u2A81': u'\u2A82',  # LESS-THAN OR SLANTED EQUAL TO WITH DOT ABOVE
    u'\u2A82': u'\u2A81',  # GREATER-THAN OR SLANTED EQUAL TO WITH DOT ABOVE
    u'\u2A83': u'\u2A84',  # LESS-THAN OR SLANTED EQUAL TO WITH DOT ABOVE RIGHT
    u'\u2A84': u'\u2A83',  # GREATER-THAN OR SLANTED EQUAL TO WITH DOT ABOVE LEFT
    u'\u2A8B': u'\u2A8C',  # LESS-THAN ABOVE DOUBLE-LINE EQUAL ABOVE GREATER-THAN
    u'\u2A8C': u'\u2A8B',  # GREATER-THAN ABOVE DOUBLE-LINE EQUAL ABOVE LESS-THAN
    u'\u2A91': u'\u2A92',  # LESS-THAN ABOVE GREATER-THAN ABOVE DOUBLE-LINE EQUAL
    u'\u2A92': u'\u2A91',  # GREATER-THAN ABOVE LESS-THAN ABOVE DOUBLE-LINE EQUAL
    # LESS-THAN ABOVE SLANTED EQUAL ABOVE GREATER-THAN ABOVE SLANTED EQUAL
    u'\u2A93': u'\u2A94',
    # GREATER-THAN ABOVE SLANTED EQUAL ABOVE LESS-THAN ABOVE SLANTED EQUAL
    u'\u2A94': u'\u2A93',
    u'\u2A95': u'\u2A96',  # SLANTED EQUAL TO OR LESS-THAN
    u'\u2A96': u'\u2A95',  # SLANTED EQUAL TO OR GREATER-THAN
    u'\u2A97': u'\u2A98',  # SLANTED EQUAL TO OR LESS-THAN WITH DOT INSIDE
    u'\u2A98': u'\u2A97',  # SLANTED EQUAL TO OR GREATER-THAN WITH DOT INSIDE
    u'\u2A99': u'\u2A9A',  # DOUBLE-LINE EQUAL TO OR LESS-THAN
    u'\u2A9A': u'\u2A99',  # DOUBLE-LINE EQUAL TO OR GREATER-THAN
    u'\u2A9B': u'\u2A9C',  # DOUBLE-LINE SLANTED EQUAL TO OR LESS-THAN
    u'\u2A9C': u'\u2A9B',  # DOUBLE-LINE SLANTED EQUAL TO OR GREATER-THAN
    u'\u2AA1': u'\u2AA2',  # DOUBLE NESTED LESS-THAN
    u'\u2AA2': u'\u2AA1',  # DOUBLE NESTED GREATER-THAN
    u'\u2AA6': u'\u2AA7',  # LESS-THAN CLOSED BY CURVE
    u'\u2AA7': u'\u2AA6',  # GREATER-THAN CLOSED BY CURVE
    u'\u2AA8': u'\u2AA9',  # LESS-THAN CLOSED BY CURVE ABOVE SLANTED EQUAL
    u'\u2AA9': u'\u2AA8',  # GREATER-THAN CLOSED BY CURVE ABOVE SLANTED EQUAL
    u'\u2AAA': u'\u2AAB',  # SMALLER THAN
    u'\u2AAB': u'\u2AAA',  # LARGER THAN
    u'\u2AAC': u'\u2AAD',  # SMALLER THAN OR EQUAL TO
    u'\u2AAD': u'\u2AAC',  # LARGER THAN OR EQUAL TO
    u'\u2AAF': u'\u2AB0',  # PRECEDES ABOVE SINGLE-LINE EQUALS SIGN
    u'\u2AB0': u'\u2AAF',  # SUCCEEDS ABOVE SINGLE-LINE EQUALS SIGN
    u'\u2AB3': u'\u2AB4',  # PRECEDES ABOVE EQUALS SIGN
    u'\u2AB4': u'\u2AB3',  # SUCCEEDS ABOVE EQUALS SIGN
    u'\u2ABB': u'\u2ABC',  # DOUBLE PRECEDES
    u'\u2ABC': u'\u2ABB',  # DOUBLE SUCCEEDS
    u'\u2ABD': u'\u2ABE',  # SUBSET WITH DOT
    u'\u2ABE': u'\u2ABD',  # SUPERSET WITH DOT
    u'\u2ABF': u'\u2AC0',  # SUBSET WITH PLUS SIGN BELOW
    u'\u2AC0': u'\u2ABF',  # SUPERSET WITH PLUS SIGN BELOW
    u'\u2AC1': u'\u2AC2',  # SUBSET WITH MULTIPLICATION SIGN BELOW
    u'\u2AC2': u'\u2AC1',  # SUPERSET WITH MULTIPLICATION SIGN BELOW
    u'\u2AC3': u'\u2AC4',  # SUBSET OF OR EQUAL TO WITH DOT ABOVE
    u'\u2AC4': u'\u2AC3',  # SUPERSET OF OR EQUAL TO WITH DOT ABOVE
    u'\u2AC5': u'\u2AC6',  # SUBSET OF ABOVE EQUALS SIGN
    u'\u2AC6': u'\u2AC5',  # SUPERSET OF ABOVE EQUALS SIGN
    u'\u2ACD': u'\u2ACE',  # SQUARE LEFT OPEN BOX OPERATOR
    u'\u2ACE': u'\u2ACD',  # SQUARE RIGHT OPEN BOX OPERATOR
    u'\u2ACF': u'\u2AD0',  # CLOSED SUBSET
    u'\u2AD0': u'\u2ACF',  # CLOSED SUPERSET
    u'\u2AD1': u'\u2AD2',  # CLOSED SUBSET OR EQUAL TO
    u'\u2AD2': u'\u2AD1',  # CLOSED SUPERSET OR EQUAL TO
    u'\u2AD3': u'\u2AD4',  # SUBSET ABOVE SUPERSET
    u'\u2AD4': u'\u2AD3',  # SUPERSET ABOVE SUBSET
    u'\u2AD5': u'\u2AD6',  # SUBSET ABOVE SUBSET
    u'\u2AD6': u'\u2AD5',  # SUPERSET ABOVE SUPERSET
    u'\u2ADE': u'\u22A6',  # SHORT LEFT TACK
    u'\u2AE3': u'\u22A9',  # DOUBLE VERTICAL BAR LEFT TURNSTILE
    u'\u2AE4': u'\u22A8',  # VERTICAL BAR DOUBLE LEFT TURNSTILE
    u'\u2AE5': u'\u22AB',  # DOUBLE VERTICAL BAR DOUBLE LEFT TURNSTILE
    u'\u2AEC': u'\u2AED',  # DOUBLE STROKE NOT SIGN
    u'\u2AED': u'\u2AEC',  # REVERSED DOUBLE STROKE NOT SIGN
    u'\u2AF7': u'\u2AF8',  # TRIPLE NESTED LESS-THAN
    u'\u2AF8': u'\u2AF7',  # TRIPLE NESTED GREATER-THAN
    u'\u2AF9': u'\u2AFA',  # DOUBLE-LINE SLANTED LESS-THAN OR EQUAL TO
    u'\u2AFA': u'\u2AF9',  # DOUBLE-LINE SLANTED GREATER-THAN OR EQUAL TO
    u'\u2E02': u'\u2E03',  # LEFT SUBSTITUTION BRACKET
    u'\u2E03': u'\u2E02',  # RIGHT SUBSTITUTION BRACKET
    u'\u2E04': u'\u2E05',  # LEFT DOTTED SUBSTITUTION BRACKET
    u'\u2E05': u'\u2E04',  # RIGHT DOTTED SUBSTITUTION BRACKET
    u'\u2E09': u'\u2E0A',  # LEFT TRANSPOSITION BRACKET
    u'\u2E0A': u'\u2E09',  # RIGHT TRANSPOSITION BRACKET
    u'\u2E0C': u'\u2E0D',  # LEFT RAISED OMISSION BRACKET
    u'\u2E0D': u'\u2E0C',  # RIGHT RAISED OMISSION BRACKET
    u'\u2E1C': u'\u2E1D',  # LEFT LOW PARAPHRASE BRACKET
    u'\u2E1D': u'\u2E1C',  # RIGHT LOW PARAPHRASE BRACKET
    u'\u2E20': u'\u2E21',  # LEFT VERTICAL BAR WITH QUILL
    u'\u2E21': u'\u2E20',  # RIGHT VERTICAL BAR WITH QUILL
    u'\u2E22': u'\u2E23',  # TOP LEFT HALF BRACKET
    u'\u2E23': u'\u2E22',  # TOP RIGHT HALF BRACKET
    u'\u2E24': u'\u2E25',  # BOTTOM LEFT HALF BRACKET
    u'\u2E25': u'\u2E24',  # BOTTOM RIGHT HALF BRACKET
    u'\u2E26': u'\u2E27',  # LEFT SIDEWAYS U BRACKET
    u'\u2E27': u'\u2E26',  # RIGHT SIDEWAYS U BRACKET
    u'\u2E28': u'\u2E29',  # LEFT DOUBLE PARENTHESIS
    u'\u2E29': u'\u2E28',  # RIGHT DOUBLE PARENTHESIS
    u'\u3008': u'\u3009',  # LEFT ANGLE BRACKET
    u'\u3009': u'\u3008',  # RIGHT ANGLE BRACKET
    u'\u300A': u'\u300B',  # LEFT DOUBLE ANGLE BRACKET
    u'\u300B': u'\u300A',  # RIGHT DOUBLE ANGLE BRACKET
    u'\u300C': u'\u300D',  # [BEST FIT] LEFT CORNER BRACKET
    u'\u300D': u'\u300C',  # [BEST FIT] RIGHT CORNER BRACKET
    u'\u300E': u'\u300F',  # [BEST FIT] LEFT WHITE CORNER BRACKET
    u'\u300F': u'\u300E',  # [BEST FIT] RIGHT WHITE CORNER BRACKET
    u'\u3010': u'\u3011',  # LEFT BLACK LENTICULAR BRACKET
    u'\u3011': u'\u3010',  # RIGHT BLACK LENTICULAR BRACKET
    u'\u3014': u'\u3015',  # LEFT TORTOISE SHELL BRACKET
    u'\u3015': u'\u3014',  # RIGHT TORTOISE SHELL BRACKET
    u'\u3016': u'\u3017',  # LEFT WHITE LENTICULAR BRACKET
    u'\u3017': u'\u3016',  # RIGHT WHITE LENTICULAR BRACKET
    u'\u3018': u'\u3019',  # LEFT WHITE TORTOISE SHELL BRACKET
    u'\u3019': u'\u3018',  # RIGHT WHITE TORTOISE SHELL BRACKET
    u'\u301A': u'\u301B',  # LEFT WHITE SQUARE BRACKET
    u'\u301B': u'\u301A',  # RIGHT WHITE SQUARE BRACKET
    u'\uFE59': u'\uFE5A',  # SMALL LEFT PARENTHESIS
    u'\uFE5A': u'\uFE59',  # SMALL RIGHT PARENTHESIS
    u'\uFE5B': u'\uFE5C',  # SMALL LEFT CURLY BRACKET
    u'\uFE5C': u'\uFE5B',  # SMALL RIGHT CURLY BRACKET
    u'\uFE5D': u'\uFE5E',  # SMALL LEFT TORTOISE SHELL BRACKET
    u'\uFE5E': u'\uFE5D',  # SMALL RIGHT TORTOISE SHELL BRACKET
    u'\uFE64': u'\uFE65',  # SMALL LESS-THAN SIGN
    u'\uFE65': u'\uFE64',  # SMALL GREATER-THAN SIGN
    u'\uFF08': u'\uFF09',  # FULLWIDTH LEFT PARENTHESIS
    u'\uFF09': u'\uFF08',  # FULLWIDTH RIGHT PARENTHESIS
    u'\uFF1C': u'\uFF1E',  # FULLWIDTH LESS-THAN SIGN
    u'\uFF1E': u'\uFF1C',  # FULLWIDTH GREATER-THAN SIGN
    u'\uFF3B': u'\uFF3D',  # FULLWIDTH LEFT SQUARE BRACKET
    u'\uFF3D': u'\uFF3B',  # FULLWIDTH RIGHT SQUARE BRACKET
    u'\uFF5B': u'\uFF5D',  # FULLWIDTH LEFT CURLY BRACKET
    u'\uFF5D': u'\uFF5B',  # FULLWIDTH RIGHT CURLY BRACKET
    u'\uFF5F': u'\uFF60',  # FULLWIDTH LEFT WHITE PARENTHESIS
    u'\uFF60': u'\uFF5F',  # FULLWIDTH RIGHT WHITE PARENTHESIS
    u'\uFF62': u'\uFF63',  # [BEST FIT] HALFWIDTH LEFT CORNER BRACKET
    u'\uFF63': u'\uFF62',  # [BEST FIT] HALFWIDTH RIGHT CORNER BRACKET
}
