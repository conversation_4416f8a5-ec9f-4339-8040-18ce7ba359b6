{% extends "base.html" %}

{% block title %}تقرير الحليب الشهري - {{ current_month }}/{{ current_year }}{% endblock %}

{% block extra_css %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<style>
    .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .stat-card h5 {
        font-size: 0.9rem;
        opacity: 0.9;
        margin-bottom: 10px;
    }
    
    .stat-card h2 {
        font-size: 2rem;
        font-weight: bold;
        margin: 0;
    }
    
    .chart-container {
        background: white;
        border-radius: 15px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    
    .cow-performance-card {
        background: white;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 10px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        border-left: 4px solid #28a745;
        transition: all 0.3s ease;
    }

    .cow-performance-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.15);
        border-left-color: #007bff;
    }

    .cow-link {
        transition: color 0.3s ease;
    }

    .cow-link:hover {
        color: #007bff !important;
    }
    
    .month-selector {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .table-responsive {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .table th {
        background-color: #f8f9fa;
        border: none;
        font-weight: 600;
    }
    
    .badge-success { background-color: #28a745; }
    .badge-warning { background-color: #ffc107; }
    .badge-info { background-color: #17a2b8; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3 mb-0">
                <i class="fas fa-calendar-alt text-primary"></i>
                تقرير الحليب الشهري - {{ current_month }}/{{ current_year }}
            </h1>
            <p class="text-muted">من {{ first_day.strftime('%d/%m/%Y') }} إلى {{ last_day.strftime('%d/%m/%Y') }}</p>
        </div>
    </div>

    <!-- Month Selector -->
    <div class="month-selector">
        <form method="GET" class="row g-3 align-items-end">
            <div class="col-md-4">
                <label for="year" class="form-label">السنة</label>
                <select name="year" id="year" class="form-select">
                    {% for y in range(2020, 2030) %}
                    <option value="{{ y }}" {% if y == current_year %}selected{% endif %}>{{ y }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-4">
                <label for="month" class="form-label">الشهر</label>
                <select name="month" id="month" class="form-select">
                    {% set months = [
                        (1, 'يناير'), (2, 'فبراير'), (3, 'مارس'), (4, 'أبريل'),
                        (5, 'مايو'), (6, 'يونيو'), (7, 'يوليو'), (8, 'أغسطس'),
                        (9, 'سبتمبر'), (10, 'أكتوبر'), (11, 'نوفمبر'), (12, 'ديسمبر')
                    ] %}
                    {% for month_num, month_name in months %}
                    <option value="{{ month_num }}" {% if month_num == current_month %}selected{% endif %}>{{ month_name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-4">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i>
                    عرض التقرير
                </button>
            </div>
        </form>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <h5>إجمالي الإنتاج</h5>
                <h2>{{ "%.1f"|format(monthly_stats.total_milk) }} لتر</h2>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <h5>المتوسط اليومي</h5>
                <h2>{{ "%.1f"|format(monthly_stats.average_daily) }} لتر</h2>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <h5>الأيام المنتجة</h5>
                <h2>{{ monthly_stats.productive_days }}/{{ monthly_stats.total_days }}</h2>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <h5>أفضل يوم</h5>
                <h2>{{ "%.1f"|format(monthly_stats.best_day.total) }} لتر</h2>
                <small>{{ monthly_stats.best_day.date }}</small>
            </div>
        </div>
    </div>

    <!-- Charts -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="chart-container">
                <h5 class="mb-3">
                    <i class="fas fa-chart-line text-primary"></i>
                    الإنتاج اليومي خلال الشهر
                </h5>
                <canvas id="dailyChart" height="100"></canvas>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-8">
            <div class="chart-container">
                <h5 class="mb-3">
                    <i class="fas fa-chart-bar text-success"></i>
                    أداء الأبقار (أفضل 10)
                </h5>
                <canvas id="cowChart" height="120"></canvas>
            </div>
        </div>
        <div class="col-md-4">
            <div class="chart-container">
                <h5 class="mb-3">
                    <i class="fas fa-trophy text-warning"></i>
                    أفضل الأبقار إنتاجاً
                </h5>
                {% for cow in monthly_stats.cow_performance[:5] %}
                <div class="cow-performance-card">
                    <a href="{{ url_for('cow_monthly_report', cow_id=cow.cow_id, year=current_year, month=current_month) }}"
                       class="text-decoration-none text-dark cow-link">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{{ cow.cow_name }}</h6>
                                <small class="text-muted">{{ cow.cow_id }}</small>
                            </div>
                            <div class="text-end">
                                <span class="badge badge-success">{{ "%.1f"|format(cow.total_milk) }} لتر</span>
                                <br>
                                <small class="text-muted">{{ "%.1f"|format(cow.average_daily) }} لتر/يوم</small>
                            </div>
                        </div>
                    </a>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Detailed Table -->
    <div class="row">
        <div class="col-12">
            <div class="chart-container">
                <h5 class="mb-3">
                    <i class="fas fa-table text-info"></i>
                    تفاصيل الإنتاج اليومي
                </h5>
                
                {% if monthly_records %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>البقرة</th>
                                <th>حليب الصباح</th>
                                <th>حليب المساء</th>
                                <th>الإجمالي اليومي</th>
                                <th>أيام الحليب</th>
                                <th>ملاحظات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in monthly_records %}
                            <tr>
                                <td>{{ record.record_date }}</td>
                                <td>
                                    <a href="{{ url_for('cow_monthly_report', cow_id=record.cow_id, year=current_year, month=current_month) }}"
                                       class="text-decoration-none cow-link">
                                        <strong>{{ record.cow_name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ record.cow_id }}</small>
                                    </a>
                                </td>
                                <td>{{ "%.1f"|format(record.morning_milk) }} لتر</td>
                                <td>{{ "%.1f"|format(record.evening_milk) }} لتر</td>
                                <td>
                                    <span class="badge badge-info">{{ "%.1f"|format(record.daily_total) }} لتر</span>
                                </td>
                                <td>{{ record.days_in_milk }} يوم</td>
                                <td>{{ record.notes or '-' }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد سجلات حليب لهذا الشهر</h5>
                    <p class="text-muted">ابدأ بإضافة سجلات الحليب اليومية</p>
                    <a href="{{ url_for('milk_tracking') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        إضافة سجل حليب
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// بيانات الرسم البياني
const chartData = {{ chart_data|tojson }};

// رسم بياني للإنتاج اليومي
const dailyCtx = document.getElementById('dailyChart').getContext('2d');
new Chart(dailyCtx, {
    type: 'line',
    data: {
        labels: chartData.dates.map(date => {
            const d = new Date(date);
            return d.getDate() + '/' + (d.getMonth() + 1);
        }),
        datasets: [{
            label: 'الإنتاج اليومي (لتر)',
            data: chartData.daily_totals,
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                title: {
                    display: true,
                    text: 'الكمية (لتر)'
                }
            },
            x: {
                title: {
                    display: true,
                    text: 'التاريخ'
                }
            }
        }
    }
});

// رسم بياني لأداء الأبقار
const cowCtx = document.getElementById('cowChart').getContext('2d');
const topCows = chartData.cow_data.slice(0, 10);
new Chart(cowCtx, {
    type: 'bar',
    data: {
        labels: topCows.map(cow => cow.cow_name),
        datasets: [{
            label: 'إجمالي الإنتاج (لتر)',
            data: topCows.map(cow => cow.total),
            backgroundColor: [
                '#28a745', '#17a2b8', '#ffc107', '#dc3545', '#6f42c1',
                '#fd7e14', '#20c997', '#6610f2', '#e83e8c', '#6c757d'
            ],
            borderWidth: 0
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                title: {
                    display: true,
                    text: 'الكمية (لتر)'
                }
            }
        }
    }
});
</script>
{% endblock %}
