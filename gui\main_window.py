# -*- coding: utf-8 -*-
"""
النافذة الرئيسية - Main Window
الواجهة الرسومية الرئيسية للبرنامج
"""

import sys
import os
from datetime import date, datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QTabWidget, QLabel, QPushButton, 
                            QFrame, QGridLayout, QMessageBox, QStatusBar,
                            QMenuBar, QAction, QToolBar, QSplitter)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QIcon, QPalette, QColor

from database.db_manager import DatabaseManager
from gui.herd_management import HerdManagementWidget
from gui.feed_management import FeedManagementWidget
from gui.milk_tracking import MilkTrackingWidget
from gui.reports_dashboard import ReportsDashboardWidget

class MainWindow(QMainWindow):
    """النافذة الرئيسية للبرنامج"""
    
    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.init_ui()
        self.setup_timer()
        self.load_dashboard_data()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("نظام إدارة مزرعة الأبقار الحلوب - Dairy Farm Management System")
        self.setGeometry(100, 100, 1400, 900)
        self.setMinimumSize(1200, 800)
        
        # إعداد الألوان والخطوط
        self.setup_style()
        
        # إنشاء القوائم
        self.create_menus()
        
        # إنشاء شريط الأدوات
        self.create_toolbar()
        
        # إنشاء الواجهة المركزية
        self.create_central_widget()
        
        # إنشاء شريط الحالة
        self.create_status_bar()
        
        # إعداد الاختصارات
        self.setup_shortcuts()
    
    def setup_style(self):
        """إعداد الألوان والخطوط"""
        # إعداد الخط العربي
        font = QFont("Arial", 10)
        font.setStyleHint(QFont.SansSerif)
        self.setFont(font)
        
        # إعداد الألوان
        palette = QPalette()
        palette.setColor(QPalette.Window, QColor(240, 240, 240))
        palette.setColor(QPalette.WindowText, QColor(50, 50, 50))
        self.setPalette(palette)
        
        # تطبيق ستايل مخصص
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QTabWidget::pane {
                border: 1px solid #c0c0c0;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #e0e0e0;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #2E86AB;
            }
            QPushButton {
                background-color: #2E86AB;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1E5F7A;
            }
            QPushButton:pressed {
                background-color: #0E3F5A;
            }
            QLabel {
                color: #333333;
            }
            QFrame {
                background-color: white;
                border: 1px solid #d0d0d0;
                border-radius: 4px;
            }
        """)
    
    def create_menus(self):
        """إنشاء القوائم"""
        menubar = self.menuBar()
        
        # قائمة الملف
        file_menu = menubar.addMenu('ملف')
        
        new_action = QAction('جديد', self)
        new_action.setShortcut('Ctrl+N')
        new_action.triggered.connect(self.new_file)
        file_menu.addAction(new_action)
        
        open_action = QAction('فتح', self)
        open_action.setShortcut('Ctrl+O')
        open_action.triggered.connect(self.open_file)
        file_menu.addAction(open_action)
        
        save_action = QAction('حفظ', self)
        save_action.setShortcut('Ctrl+S')
        save_action.triggered.connect(self.save_file)
        file_menu.addAction(save_action)
        
        file_menu.addSeparator()
        
        backup_action = QAction('نسخة احتياطية', self)
        backup_action.triggered.connect(self.create_backup)
        file_menu.addAction(backup_action)
        
        restore_action = QAction('استعادة', self)
        restore_action.triggered.connect(self.restore_backup)
        file_menu.addAction(restore_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction('خروج', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # قائمة البيانات
        data_menu = menubar.addMenu('البيانات')
        
        import_action = QAction('استيراد بيانات', self)
        import_action.triggered.connect(self.import_data)
        data_menu.addAction(import_action)
        
        export_action = QAction('تصدير بيانات', self)
        export_action.triggered.connect(self.export_data)
        data_menu.addAction(export_action)
        
        # قائمة التقارير
        reports_menu = menubar.addMenu('التقارير')
        
        daily_report_action = QAction('تقرير يومي', self)
        daily_report_action.triggered.connect(self.generate_daily_report)
        reports_menu.addAction(daily_report_action)
        
        weekly_report_action = QAction('تقرير أسبوعي', self)
        weekly_report_action.triggered.connect(self.generate_weekly_report)
        reports_menu.addAction(weekly_report_action)
        
        monthly_report_action = QAction('تقرير شهري', self)
        monthly_report_action.triggered.connect(self.generate_monthly_report)
        reports_menu.addAction(monthly_report_action)
        
        # قائمة المساعدة
        help_menu = menubar.addMenu('مساعدة')
        
        about_action = QAction('حول البرنامج', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
        
        help_action = QAction('دليل المستخدم', self)
        help_action.triggered.connect(self.show_help)
        help_menu.addAction(help_action)
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = QToolBar()
        self.addToolBar(toolbar)
        
        # أزرار سريعة
        add_cow_action = QAction('إضافة بقرة', self)
        add_cow_action.triggered.connect(self.quick_add_cow)
        toolbar.addAction(add_cow_action)
        
        add_milk_action = QAction('تسجيل حليب', self)
        add_milk_action.triggered.connect(self.quick_add_milk)
        toolbar.addAction(add_milk_action)
        
        toolbar.addSeparator()
        
        refresh_action = QAction('تحديث', self)
        refresh_action.triggered.connect(self.refresh_data)
        toolbar.addAction(refresh_action)
        
        backup_action = QAction('نسخ احتياطي', self)
        backup_action.triggered.connect(self.create_backup)
        toolbar.addAction(backup_action)
    
    def create_central_widget(self):
        """إنشاء الواجهة المركزية"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # تخطيط رئيسي
        main_layout = QVBoxLayout(central_widget)
        
        # لوحة المعلومات السريعة
        self.create_dashboard_panel(main_layout)
        
        # التبويبات الرئيسية
        self.create_main_tabs(main_layout)
    
    def create_dashboard_panel(self, parent_layout):
        """إنشاء لوحة المعلومات السريعة"""
        dashboard_frame = QFrame()
        dashboard_frame.setMaximumHeight(120)
        dashboard_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2E86AB, stop:1 #A23B72);
                border-radius: 8px;
                color: white;
            }
            QLabel {
                color: white;
                font-weight: bold;
            }
        """)
        
        dashboard_layout = QGridLayout(dashboard_frame)
        
        # بطاقات المعلومات
        self.total_cows_label = QLabel("0")
        self.total_cows_label.setAlignment(Qt.AlignCenter)
        self.total_cows_label.setStyleSheet("font-size: 24px; font-weight: bold;")
        
        self.active_cows_label = QLabel("0")
        self.active_cows_label.setAlignment(Qt.AlignCenter)
        self.active_cows_label.setStyleSheet("font-size: 24px; font-weight: bold;")
        
        self.daily_milk_label = QLabel("0.0")
        self.daily_milk_label.setAlignment(Qt.AlignCenter)
        self.daily_milk_label.setStyleSheet("font-size: 24px; font-weight: bold;")
        
        self.avg_milk_label = QLabel("0.0")
        self.avg_milk_label.setAlignment(Qt.AlignCenter)
        self.avg_milk_label.setStyleSheet("font-size: 24px; font-weight: bold;")
        
        # إضافة البطاقات
        dashboard_layout.addWidget(QLabel("إجمالي الأبقار"), 0, 0)
        dashboard_layout.addWidget(self.total_cows_label, 1, 0)
        
        dashboard_layout.addWidget(QLabel("الأبقار النشطة"), 0, 1)
        dashboard_layout.addWidget(self.active_cows_label, 1, 1)
        
        dashboard_layout.addWidget(QLabel("إنتاج اليوم (لتر)"), 0, 2)
        dashboard_layout.addWidget(self.daily_milk_label, 1, 2)
        
        dashboard_layout.addWidget(QLabel("متوسط الإنتاج"), 0, 3)
        dashboard_layout.addWidget(self.avg_milk_label, 1, 3)
        
        parent_layout.addWidget(dashboard_frame)
    
    def create_main_tabs(self, parent_layout):
        """إنشاء التبويبات الرئيسية"""
        self.tab_widget = QTabWidget()
        
        # تبويب إدارة القطيع
        self.herd_widget = HerdManagementWidget(self.db_manager)
        self.tab_widget.addTab(self.herd_widget, "إدارة القطيع")
        
        # تبويب إدارة الأعلاف
        self.feed_widget = FeedManagementWidget(self.db_manager)
        self.tab_widget.addTab(self.feed_widget, "إدارة الأعلاف")
        
        # تبويب تتبع الحليب
        self.milk_widget = MilkTrackingWidget(self.db_manager)
        self.tab_widget.addTab(self.milk_widget, "تتبع الحليب")
        
        # تبويب التقارير
        self.reports_widget = ReportsDashboardWidget(self.db_manager)
        self.tab_widget.addTab(self.reports_widget, "التقارير والإحصائيات")
        
        # ربط الإشارات
        self.herd_widget.data_changed.connect(self.refresh_dashboard)
        self.milk_widget.data_changed.connect(self.refresh_dashboard)
        
        parent_layout.addWidget(self.tab_widget)
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # معلومات الحالة
        self.status_label = QLabel("جاهز")
        self.status_bar.addWidget(self.status_label)
        
        # معلومات قاعدة البيانات
        self.db_status_label = QLabel("قاعدة البيانات: متصلة")
        self.status_bar.addPermanentWidget(self.db_status_label)
        
        # الوقت الحالي
        self.time_label = QLabel()
        self.status_bar.addPermanentWidget(self.time_label)
    
    def setup_shortcuts(self):
        """إعداد اختصارات لوحة المفاتيح"""
        pass
    
    def setup_timer(self):
        """إعداد المؤقت لتحديث البيانات"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)  # تحديث كل ثانية
        
        # مؤقت تحديث البيانات
        self.data_timer = QTimer()
        self.data_timer.timeout.connect(self.refresh_dashboard)
        self.data_timer.start(60000)  # تحديث كل دقيقة
    
    def update_time(self):
        """تحديث الوقت في شريط الحالة"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(current_time)
    
    def load_dashboard_data(self):
        """تحميل بيانات لوحة المعلومات"""
        try:
            # إحصائيات القطيع
            herd_stats = self.db_manager.get_herd_statistics()
            
            self.total_cows_label.setText(str(herd_stats['total_cows']))
            self.active_cows_label.setText(str(herd_stats['active_cows']))
            self.avg_milk_label.setText(f"{herd_stats['herd_average_milk']:.1f}")
            
            # إنتاج اليوم
            today_summary = self.db_manager.get_daily_milk_summary(date.today())
            self.daily_milk_label.setText(f"{today_summary['total_milk']:.1f}")
            
            self.status_label.setText("تم تحديث البيانات")
            
        except Exception as e:
            self.status_label.setText(f"خطأ في تحميل البيانات: {str(e)}")
    
    def refresh_dashboard(self):
        """تحديث لوحة المعلومات"""
        self.load_dashboard_data()
    
    def refresh_data(self):
        """تحديث جميع البيانات"""
        self.refresh_dashboard()
        self.herd_widget.refresh_data()
        self.feed_widget.refresh_data()
        self.milk_widget.refresh_data()
        self.reports_widget.refresh_data()
        
        self.status_label.setText("تم تحديث جميع البيانات")
    
    # ===== وظائف القوائم =====
    
    def new_file(self):
        """إنشاء ملف جديد"""
        reply = QMessageBox.question(self, 'تأكيد', 
                                   'هل تريد إنشاء قاعدة بيانات جديدة؟ سيتم فقدان البيانات الحالية.',
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            # إنشاء قاعدة بيانات جديدة
            pass
    
    def open_file(self):
        """فتح ملف"""
        pass
    
    def save_file(self):
        """حفظ الملف"""
        self.status_label.setText("تم حفظ البيانات")
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        try:
            backup_path = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
            if self.db_manager.backup_database(backup_path):
                QMessageBox.information(self, 'نجح', f'تم إنشاء النسخة الاحتياطية: {backup_path}')
            else:
                QMessageBox.warning(self, 'خطأ', 'فشل في إنشاء النسخة الاحتياطية')
        except Exception as e:
            QMessageBox.critical(self, 'خطأ', f'خطأ في إنشاء النسخة الاحتياطية: {str(e)}')
    
    def restore_backup(self):
        """استعادة نسخة احتياطية"""
        pass
    
    def import_data(self):
        """استيراد بيانات"""
        pass
    
    def export_data(self):
        """تصدير بيانات"""
        pass
    
    def generate_daily_report(self):
        """إنشاء تقرير يومي"""
        self.reports_widget.generate_daily_report()
    
    def generate_weekly_report(self):
        """إنشاء تقرير أسبوعي"""
        self.reports_widget.generate_weekly_report()
    
    def generate_monthly_report(self):
        """إنشاء تقرير شهري"""
        self.reports_widget.generate_monthly_report()
    
    def show_about(self):
        """عرض معلومات البرنامج"""
        QMessageBox.about(self, 'حول البرنامج', 
                         'نظام إدارة مزرعة الأبقار الحلوب\n'
                         'الإصدار 1.0\n'
                         'برنامج شامل لإدارة القطيع والأعلاف وتتبع الإنتاج')
    
    def show_help(self):
        """عرض دليل المستخدم"""
        QMessageBox.information(self, 'دليل المستخدم', 
                              'دليل المستخدم متاح في ملف المساعدة المرفق مع البرنامج')
    
    def quick_add_cow(self):
        """إضافة بقرة سريعة"""
        self.tab_widget.setCurrentIndex(0)  # الانتقال لتبويب القطيع
        self.herd_widget.add_new_cow()
    
    def quick_add_milk(self):
        """تسجيل حليب سريع"""
        self.tab_widget.setCurrentIndex(2)  # الانتقال لتبويب الحليب
        self.milk_widget.quick_add_record()
    
    def closeEvent(self, event):
        """حدث إغلاق النافذة"""
        reply = QMessageBox.question(self, 'تأكيد الخروج', 
                                   'هل تريد حفظ البيانات قبل الخروج؟',
                                   QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel)
        
        if reply == QMessageBox.Yes:
            self.save_file()
            event.accept()
        elif reply == QMessageBox.No:
            event.accept()
        else:
            event.ignore()
