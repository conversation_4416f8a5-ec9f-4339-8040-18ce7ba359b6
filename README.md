# 🐄 نظام إدارة مزرعة الأبقار الحلوب
## Dairy Farm Management System

نظام شامل ومتطور لإدارة مزارع الأبقار الحلوب باللغة العربية، مبني بتقنية Flask مع واجهة ويب حديثة وسهلة الاستخدام.

## ✨ الميزات الرئيسية

### 🐮 إدارة القطيع
- **تسجيل شامل للأبقار**: رقم البقرة، الاسم، السلالة، العمر، الوزن
- **تتبع الحالة الصحية**: سليمة، مريضة، حامل، جافة
- **معلومات الإنتاج**: تواريخ الولادة، أيام الحليب، مراحل الإنتاج
- **فلترة وبحث متقدم**: حسب السلالة، الحالة، الاسم أو الرقم

### 🥛 تتبع إنتاج الحليب
- **تسجيل يومي**: حليب الصباح والمساء لكل بقرة
- **إحصائيات فورية**: إجمالي الإنتاج، المتوسط، أعلى وأقل إنتاج
- **رسوم بيانية تفاعلية**: تصور بيانات الإنتاج
- **تنبيهات ذكية**: للأبقار التي لم يسجل إنتاجها

### 🌾 إدارة الأعلاف المتقدمة
- **خلطات علف مخصصة**: تركيب خلطات حسب الاحتياجات
- **تحليل غذائي شامل**: البروتين، الطاقة، الألياف، المعادن
- **حساب التكلفة**: تكلفة الكيلو والطن
- **تقييم الجودة**: نقاط جودة مع تحذيرات وتوصيات

### 📊 التحليل والتقارير
- **لوحة معلومات تفاعلية**: إحصائيات فورية ومؤشرات أداء
- **تقارير PDF**: تقارير يومية وأسبوعية وشهرية
- **تصدير Excel**: بيانات قابلة للتحليل
- **تحليل التغذية**: كفاءة الخلطات والاحتياجات الغذائية

### 🔬 التحليل الغذائي المتقدم
- **حساب الاحتياجات الفردية**: حسب الوزن والإنتاج ومرحلة الحليب
- **تحليل كفاية العلف**: مقارنة الاحتياجات مع المتوفر
- **توصيات ذكية**: تحسين الخلطات والتغذية
- **تحليل اقتصادي**: كفاءة التكلفة والربحية

## 🛠️ التقنيات المستخدمة

- **Backend**: Python 3.8+, Flask, SQLAlchemy
- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap 5 (RTL)
- **Database**: SQLite (قابل للترقية لـ PostgreSQL/MySQL)
- **Charts**: Chart.js, Plotly
- **Reports**: ReportLab (PDF), OpenPyXL (Excel)
- **Arabic Support**: Arabic-reshaper, Python-bidi

## 📋 متطلبات النظام

- Python 3.8 أو أحدث
- 4 GB RAM (الحد الأدنى)
- 1 GB مساحة تخزين
- متصفح ويب حديث (Chrome, Firefox, Safari, Edge)

## 🚀 التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone https://github.com/your-repo/dairy-farm-system.git
cd dairy-farm-system
```

### 2. إنشاء بيئة افتراضية
```bash
python -m venv venv

# Windows
venv\Scripts\activate

# Linux/Mac
source venv/bin/activate
```

### 3. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 4. تشغيل النظام
```bash
python main.py
```

سيتم فتح النظام تلقائياً في المتصفح على العنوان: `http://localhost:5000`

## 📱 كيفية الاستخدام

### البداية السريعة
1. **إضافة الأبقار**: ابدأ بإضافة أبقارك من قسم "إدارة القطيع"
2. **إنشاء خلطات العلف**: أنشئ خلطات العلف المناسبة
3. **تسجيل الإنتاج**: سجل إنتاج الحليب يومياً
4. **مراجعة التقارير**: تابع الأداء من خلال التقارير

### البيانات التجريبية
عند التشغيل لأول مرة، سيسألك النظام عن إنشاء بيانات تجريبية تشمل:
- 5 أبقار بسلالات مختلفة
- 3 خلطات علف متنوعة
- سجلات حليب لآخر 7 أيام

## 📊 لوحة المعلومات

تعرض لوحة المعلومات الرئيسية:
- **إحصائيات القطيع**: العدد الإجمالي والنشط
- **إنتاج اليوم**: الكمية الإجمالية والمتوسط
- **أعلى المنتجات**: ترتيب الأبقار حسب الإنتاج
- **خلطات العلف النشطة**: الخلطات المستخدمة حالياً
- **الأحداث الأخيرة**: تنبيهات وملاحظات مهمة

## 🔧 الإعدادات المتقدمة

### تخصيص المعايير الغذائية
يمكن تعديل المعايير الغذائية في ملف `config/nutrition_standards.py`:
- معايير NRC للأبقار الحلوب
- قاعدة بيانات المكونات العلفية
- حدود التحذير للقيم الغذائية

### إعداد قاعدة البيانات
للاستخدام في بيئة الإنتاج، يمكن تغيير قاعدة البيانات في `database/db_manager.py`

## 📈 التقارير المتاحة

### تقارير الحليب
- **تقرير يومي**: إنتاج جميع الأبقار ليوم محدد
- **تقرير أسبوعي**: اتجاهات الإنتاج الأسبوعية
- **تقرير شهري**: تحليل الأداء الشهري

### تقارير القطيع
- **ملخص القطيع**: إحصائيات شاملة للقطيع
- **تقرير الصحة**: حالة الأبقار الصحية
- **تقرير الإنتاجية**: تحليل الأداء الإنتاجي

### تقارير التغذية
- **تحليل الخلطات**: تقييم جودة خلطات العلف
- **كفاءة التغذية**: تحليل التكلفة والعائد
- **توصيات التحسين**: اقتراحات لتحسين التغذية

## 🔒 الأمان والنسخ الاحتياطي

- **نسخ احتياطية تلقائية**: يمكن جدولة نسخ احتياطية دورية
- **تصدير البيانات**: إمكانية تصدير جميع البيانات
- **استعادة البيانات**: استعادة من النسخ الاحتياطية

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

**خطأ في تثبيت المتطلبات:**
```bash
pip install --upgrade pip
pip install -r requirements.txt --force-reinstall
```

**خطأ في قاعدة البيانات:**
```bash
# حذف قاعدة البيانات وإعادة إنشائها
rm dairy_farm.db
python main.py
```

**مشكلة في عرض النصوص العربية:**
- تأكد من تثبيت الخطوط العربية في النظام
- استخدم متصفح حديث يدعم RTL

## 🤝 المساهمة

نرحب بمساهماتكم لتطوير النظام:
1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push للـ branch
5. إنشاء Pull Request

## 📞 الدعم والمساعدة

- **الوثائق**: راجع ملفات المساعدة في المجلد `docs/`
- **المشاكل**: أبلغ عن المشاكل في GitHub Issues
- **الاقتراحات**: نرحب بأفكاركم لتطوير النظام

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف `LICENSE` للتفاصيل.

## 🙏 شكر وتقدير

- مجتمع Python العربي
- مطوري Flask و Bootstrap
- جميع المساهمين في المشروع

---

**تم تطوير هذا النظام بعناية لخدمة مربي الأبقار في الوطن العربي** 🇸🇦🇪🇬🇦🇪🇯🇴🇱🇧🇸🇾🇮🇶🇰🇼🇶🇦🇧🇭🇴🇲🇾🇪

**نتمنى لكم تجربة ممتعة ومفيدة!** 🐄💚
