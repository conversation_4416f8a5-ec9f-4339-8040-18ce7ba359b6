# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات - Database Manager
يدير جميع عمليات قاعدة البيانات SQLite
"""

import sqlite3
import json
import os
from datetime import date, datetime
from typing import List, Dict, Optional, Any
from models.cow import Cow
from models.feed_mix import FeedMix

class DatabaseManager:
    """مدير قاعدة البيانات"""
    
    def __init__(self, db_path: str = "dairy_farm.db"):
        self.db_path = db_path
        self.init_database()
    
    def get_connection(self) -> sqlite3.Connection:
        """إنشاء اتصال بقاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # للحصول على النتائج كقواميس
        return conn
    
    def init_database(self):
        """إنشاء جداول قاعدة البيانات"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # جدول الأبقار
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS cows (
                    cow_id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    breed TEXT NOT NULL,
                    birth_date TEXT NOT NULL,
                    weight REAL NOT NULL,
                    registration_date TEXT NOT NULL,
                    last_calving_date TEXT,
                    health_status TEXT NOT NULL,
                    lactation_number INTEGER NOT NULL,
                    is_active BOOLEAN NOT NULL,
                    notes TEXT,
                    daily_feed_intake REAL NOT NULL,
                    body_condition_score REAL NOT NULL,
                    average_daily_milk REAL NOT NULL,
                    total_lactation_milk REAL NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # جدول سجلات الحليب اليومية
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS milk_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    cow_id TEXT NOT NULL,
                    record_date TEXT NOT NULL,
                    morning_milk REAL NOT NULL,
                    evening_milk REAL NOT NULL,
                    daily_total REAL NOT NULL,
                    days_in_milk INTEGER NOT NULL,
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (cow_id) REFERENCES cows (cow_id),
                    UNIQUE(cow_id, record_date)
                )
            ''')
            
            # جدول خلطات العلف
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS feed_mixes (
                    mix_id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    description TEXT,
                    creation_date TEXT NOT NULL,
                    last_modified TEXT NOT NULL,
                    is_active BOOLEAN NOT NULL,
                    ingredients TEXT NOT NULL,
                    nutritional_analysis TEXT NOT NULL,
                    cost_per_kg REAL NOT NULL,
                    cost_per_ton REAL NOT NULL,
                    quality_score REAL NOT NULL,
                    warnings TEXT NOT NULL,
                    recommendations TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # جدول استخدام الخلطات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS feed_usage (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    mix_id TEXT NOT NULL,
                    usage_date TEXT NOT NULL,
                    quantity_kg REAL NOT NULL,
                    number_of_cows INTEGER NOT NULL,
                    cost_total REAL NOT NULL,
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (mix_id) REFERENCES feed_mixes (mix_id)
                )
            ''')
            
            # جدول الأحداث والتنبيهات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS events (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    event_type TEXT NOT NULL,
                    cow_id TEXT,
                    event_date TEXT NOT NULL,
                    description TEXT NOT NULL,
                    severity TEXT NOT NULL,
                    is_resolved BOOLEAN DEFAULT FALSE,
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (cow_id) REFERENCES cows (cow_id)
                )
            ''')
            
            # إنشاء فهارس لتحسين الأداء
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_milk_records_cow_date ON milk_records(cow_id, record_date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_milk_records_date ON milk_records(record_date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_events_date ON events(event_date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_events_cow ON events(cow_id)')
            
            conn.commit()
    
    # ===== إدارة الأبقار =====
    
    def add_cow(self, cow: Cow) -> bool:
        """إضافة بقرة جديدة"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO cows (
                        cow_id, name, breed, birth_date, weight, registration_date,
                        last_calving_date, health_status, lactation_number, is_active,
                        notes, daily_feed_intake, body_condition_score,
                        average_daily_milk, total_lactation_milk
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    cow.cow_id, cow.name, cow.breed, cow.birth_date.isoformat(),
                    cow.weight, cow.registration_date.isoformat(),
                    cow.last_calving_date.isoformat() if cow.last_calving_date else None,
                    cow.health_status, cow.lactation_number, cow.is_active,
                    cow.notes, cow.daily_feed_intake, cow.body_condition_score,
                    cow.average_daily_milk, cow.total_lactation_milk
                ))
                
                # إضافة سجلات الحليب
                for record in cow.daily_milk_records:
                    self._add_milk_record_raw(cursor, cow.cow_id, record)
                
                conn.commit()
                return True
        except sqlite3.IntegrityError:
            return False
    
    def update_cow(self, cow: Cow) -> bool:
        """تحديث بيانات البقرة"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE cows SET
                        name = ?, breed = ?, birth_date = ?, weight = ?,
                        last_calving_date = ?, health_status = ?, lactation_number = ?,
                        is_active = ?, notes = ?, daily_feed_intake = ?,
                        body_condition_score = ?, average_daily_milk = ?,
                        total_lactation_milk = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE cow_id = ?
                ''', (
                    cow.name, cow.breed, cow.birth_date.isoformat(), cow.weight,
                    cow.last_calving_date.isoformat() if cow.last_calving_date else None,
                    cow.health_status, cow.lactation_number, cow.is_active,
                    cow.notes, cow.daily_feed_intake, cow.body_condition_score,
                    cow.average_daily_milk, cow.total_lactation_milk, cow.cow_id
                ))
                conn.commit()
                return cursor.rowcount > 0
        except Exception:
            return False
    
    def get_cow(self, cow_id: str) -> Optional[Cow]:
        """الحصول على بقرة بالرقم"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM cows WHERE cow_id = ?', (cow_id,))
            row = cursor.fetchone()
            
            if not row:
                return None
            
            cow = self._row_to_cow(row)
            
            # تحميل سجلات الحليب
            cursor.execute('''
                SELECT * FROM milk_records 
                WHERE cow_id = ? 
                ORDER BY record_date
            ''', (cow_id,))
            
            milk_records = []
            for record_row in cursor.fetchall():
                milk_records.append({
                    'date': record_row['record_date'],
                    'morning_milk': record_row['morning_milk'],
                    'evening_milk': record_row['evening_milk'],
                    'daily_total': record_row['daily_total'],
                    'days_in_milk': record_row['days_in_milk'],
                    'notes': record_row['notes'] or ''
                })
            
            cow.daily_milk_records = milk_records
            return cow
    
    def get_all_cows(self, active_only: bool = True) -> List[Cow]:
        """الحصول على جميع الأبقار"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            query = 'SELECT * FROM cows'
            if active_only:
                query += ' WHERE is_active = 1'
            query += ' ORDER BY cow_id'
            
            cursor.execute(query)
            cows = []
            
            for row in cursor.fetchall():
                cow = self._row_to_cow(row)
                cows.append(cow)
            
            return cows
    
    def _row_to_cow(self, row) -> Cow:
        """تحويل صف قاعدة البيانات إلى كائن بقرة"""
        cow = Cow(
            cow_id=row['cow_id'],
            name=row['name'],
            breed=row['breed'],
            birth_date=date.fromisoformat(row['birth_date']),
            weight=row['weight']
        )
        
        cow.registration_date = date.fromisoformat(row['registration_date'])
        if row['last_calving_date']:
            cow.last_calving_date = date.fromisoformat(row['last_calving_date'])
        cow.health_status = row['health_status']
        cow.lactation_number = row['lactation_number']
        cow.is_active = bool(row['is_active'])
        cow.notes = row['notes'] or ''
        cow.daily_feed_intake = row['daily_feed_intake']
        cow.body_condition_score = row['body_condition_score']
        cow.average_daily_milk = row['average_daily_milk']
        cow.total_lactation_milk = row['total_lactation_milk']
        
        return cow
    
    # ===== إدارة سجلات الحليب =====
    
    def add_milk_record(self, cow_id: str, record_date: date, 
                       morning_milk: float, evening_milk: float, 
                       notes: str = "") -> bool:
        """إضافة سجل حليب يومي"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # الحصول على أيام الحليب
                cow = self.get_cow(cow_id)
                if not cow:
                    return False
                
                days_in_milk = cow.days_in_milk
                daily_total = morning_milk + evening_milk
                
                cursor.execute('''
                    INSERT OR REPLACE INTO milk_records 
                    (cow_id, record_date, morning_milk, evening_milk, 
                     daily_total, days_in_milk, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (cow_id, record_date.isoformat(), morning_milk, 
                      evening_milk, daily_total, days_in_milk, notes))
                
                # تحديث إحصائيات البقرة
                self._update_cow_milk_statistics(cursor, cow_id)
                
                conn.commit()
                return True
        except Exception:
            return False
    
    def _add_milk_record_raw(self, cursor, cow_id: str, record: Dict):
        """إضافة سجل حليب خام (للاستخدام الداخلي)"""
        cursor.execute('''
            INSERT OR REPLACE INTO milk_records 
            (cow_id, record_date, morning_milk, evening_milk, 
             daily_total, days_in_milk, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (cow_id, record['date'], record['morning_milk'], 
              record['evening_milk'], record['daily_total'], 
              record['days_in_milk'], record['notes']))
    
    def _update_cow_milk_statistics(self, cursor, cow_id: str):
        """تحديث إحصائيات الحليب للبقرة"""
        cursor.execute('''
            SELECT AVG(daily_total) as avg_milk, SUM(daily_total) as total_milk
            FROM milk_records 
            WHERE cow_id = ? AND days_in_milk > 0
        ''', (cow_id,))
        
        result = cursor.fetchone()
        avg_milk = result['avg_milk'] or 0.0
        total_milk = result['total_milk'] or 0.0
        
        cursor.execute('''
            UPDATE cows 
            SET average_daily_milk = ?, total_lactation_milk = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE cow_id = ?
        ''', (avg_milk, total_milk, cow_id))
    
    def get_milk_records_by_date(self, target_date: date) -> List[Dict]:
        """الحصول على سجلات الحليب ليوم محدد"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT mr.*, c.name as cow_name
                FROM milk_records mr
                JOIN cows c ON mr.cow_id = c.cow_id
                WHERE mr.record_date = ?
                ORDER BY mr.cow_id
            ''', (target_date.isoformat(),))

            records = []
            for row in cursor.fetchall():
                records.append({
                    'cow_id': row['cow_id'],
                    'cow_name': row['cow_name'],
                    'record_date': row['record_date'],
                    'morning_milk': row['morning_milk'],
                    'evening_milk': row['evening_milk'],
                    'daily_total': row['daily_total'],
                    'days_in_milk': row['days_in_milk'],
                    'notes': row['notes'] or ''
                })

            return records

    def get_milk_records_by_date_range(self, start_date: date, end_date: date) -> List[Dict]:
        """الحصول على سجلات الحليب لفترة زمنية محددة"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT mr.*, c.name as cow_name
                FROM milk_records mr
                JOIN cows c ON mr.cow_id = c.cow_id
                WHERE mr.record_date BETWEEN ? AND ?
                ORDER BY mr.record_date, mr.cow_id
            ''', (start_date.isoformat(), end_date.isoformat()))

            records = []
            for row in cursor.fetchall():
                records.append({
                    'cow_id': row['cow_id'],
                    'cow_name': row['cow_name'],
                    'record_date': row['record_date'],
                    'morning_milk': row['morning_milk'],
                    'evening_milk': row['evening_milk'],
                    'daily_total': row['daily_total'],
                    'days_in_milk': row['days_in_milk'],
                    'notes': row['notes'] or ''
                })

            return records

    # ===== إدارة خلطات العلف =====

    def add_feed_mix(self, feed_mix: FeedMix) -> bool:
        """إضافة خلطة علف جديدة"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO feed_mixes (
                        mix_id, name, description, creation_date, last_modified,
                        is_active, ingredients, nutritional_analysis, cost_per_kg,
                        cost_per_ton, quality_score, warnings, recommendations
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    feed_mix.mix_id, feed_mix.name, feed_mix.description,
                    feed_mix.creation_date.isoformat(), feed_mix.last_modified.isoformat(),
                    feed_mix.is_active, json.dumps(feed_mix.ingredients),
                    json.dumps(feed_mix.nutritional_analysis), feed_mix.cost_per_kg,
                    feed_mix.cost_per_ton, feed_mix.quality_score,
                    json.dumps(feed_mix.warnings), json.dumps(feed_mix.recommendations)
                ))
                conn.commit()
                return True
        except sqlite3.IntegrityError:
            return False

    def update_feed_mix(self, feed_mix: FeedMix) -> bool:
        """تحديث خلطة العلف"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE feed_mixes SET
                        name = ?, description = ?, last_modified = ?, is_active = ?,
                        ingredients = ?, nutritional_analysis = ?, cost_per_kg = ?,
                        cost_per_ton = ?, quality_score = ?, warnings = ?,
                        recommendations = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE mix_id = ?
                ''', (
                    feed_mix.name, feed_mix.description, feed_mix.last_modified.isoformat(),
                    feed_mix.is_active, json.dumps(feed_mix.ingredients),
                    json.dumps(feed_mix.nutritional_analysis), feed_mix.cost_per_kg,
                    feed_mix.cost_per_ton, feed_mix.quality_score,
                    json.dumps(feed_mix.warnings), json.dumps(feed_mix.recommendations),
                    feed_mix.mix_id
                ))
                conn.commit()
                return cursor.rowcount > 0
        except Exception:
            return False

    def get_feed_mix(self, mix_id: str) -> Optional[FeedMix]:
        """الحصول على خلطة علف بالرقم"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM feed_mixes WHERE mix_id = ?', (mix_id,))
            row = cursor.fetchone()

            if not row:
                return None

            return self._row_to_feed_mix(row)

    def get_all_feed_mixes(self, active_only: bool = True) -> List[FeedMix]:
        """الحصول على جميع خلطات العلف"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            query = 'SELECT * FROM feed_mixes'
            if active_only:
                query += ' WHERE is_active = 1'
            query += ' ORDER BY name'

            cursor.execute(query)
            mixes = []

            for row in cursor.fetchall():
                mix = self._row_to_feed_mix(row)
                mixes.append(mix)

            return mixes

    def _row_to_feed_mix(self, row) -> FeedMix:
        """تحويل صف قاعدة البيانات إلى كائن خلطة علف"""
        mix = FeedMix(
            mix_id=row['mix_id'],
            name=row['name'],
            description=row['description'] or ''
        )

        mix.creation_date = date.fromisoformat(row['creation_date'])
        mix.last_modified = datetime.fromisoformat(row['last_modified'])
        mix.is_active = bool(row['is_active'])
        mix.ingredients = json.loads(row['ingredients'])
        mix.nutritional_analysis = json.loads(row['nutritional_analysis'])
        mix.cost_per_kg = row['cost_per_kg']
        mix.cost_per_ton = row['cost_per_ton']
        mix.quality_score = row['quality_score']
        mix.warnings = json.loads(row['warnings'])
        mix.recommendations = json.loads(row['recommendations'])

        return mix

    # ===== إدارة الأحداث والتنبيهات =====

    def add_event(self, event_type: str, description: str, severity: str = "info",
                  cow_id: Optional[str] = None, event_date: Optional[date] = None,
                  notes: str = "") -> bool:
        """إضافة حدث أو تنبيه"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO events (event_type, cow_id, event_date, description,
                                      severity, notes)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (event_type, cow_id, (event_date or date.today()).isoformat(),
                      description, severity, notes))
                conn.commit()
                return True
        except Exception:
            return False

    def get_recent_events(self, days: int = 7, severity: Optional[str] = None) -> List[Dict]:
        """الحصول على الأحداث الأخيرة"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            query = '''
                SELECT e.*, c.name as cow_name
                FROM events e
                LEFT JOIN cows c ON e.cow_id = c.cow_id
                WHERE e.event_date >= date('now', '-{} days')
            '''.format(days)

            params = []
            if severity:
                query += ' AND e.severity = ?'
                params.append(severity)

            query += ' ORDER BY e.event_date DESC, e.created_at DESC'

            cursor.execute(query, params)
            events = []

            for row in cursor.fetchall():
                events.append({
                    'id': row['id'],
                    'event_type': row['event_type'],
                    'cow_id': row['cow_id'],
                    'cow_name': row['cow_name'],
                    'event_date': row['event_date'],
                    'description': row['description'],
                    'severity': row['severity'],
                    'is_resolved': bool(row['is_resolved']),
                    'notes': row['notes'] or '',
                    'created_at': row['created_at']
                })

            return events

    # ===== تقارير وإحصائيات =====

    def get_daily_milk_summary(self, target_date: date) -> Dict:
        """ملخص إنتاج الحليب اليومي"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT
                    COUNT(*) as total_cows,
                    SUM(daily_total) as total_milk,
                    AVG(daily_total) as average_milk,
                    MIN(daily_total) as min_milk,
                    MAX(daily_total) as max_milk
                FROM milk_records
                WHERE record_date = ?
            ''', (target_date.isoformat(),))

            result = cursor.fetchone()

            return {
                'date': target_date.isoformat(),
                'total_cows': result['total_cows'] or 0,
                'total_milk': round(result['total_milk'] or 0, 2),
                'average_milk': round(result['average_milk'] or 0, 2),
                'min_milk': round(result['min_milk'] or 0, 2),
                'max_milk': round(result['max_milk'] or 0, 2)
            }

    def get_herd_statistics(self) -> Dict:
        """إحصائيات القطيع"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # إحصائيات عامة
            cursor.execute('''
                SELECT
                    COUNT(*) as total_cows,
                    COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_cows,
                    AVG(weight) as average_weight,
                    AVG(average_daily_milk) as herd_average_milk
                FROM cows
            ''')
            general_stats = cursor.fetchone()

            # إحصائيات حسب السلالة
            cursor.execute('''
                SELECT breed, COUNT(*) as count, AVG(average_daily_milk) as avg_milk
                FROM cows
                WHERE is_active = 1
                GROUP BY breed
                ORDER BY count DESC
            ''')
            breed_stats = cursor.fetchall()

            # إحصائيات حسب الحالة الصحية
            cursor.execute('''
                SELECT health_status, COUNT(*) as count
                FROM cows
                WHERE is_active = 1
                GROUP BY health_status
            ''')
            health_stats = cursor.fetchall()

            return {
                'total_cows': general_stats['total_cows'],
                'active_cows': general_stats['active_cows'],
                'average_weight': round(general_stats['average_weight'] or 0, 1),
                'herd_average_milk': round(general_stats['herd_average_milk'] or 0, 2),
                'breed_distribution': [dict(row) for row in breed_stats],
                'health_distribution': [dict(row) for row in health_stats]
            }

    def backup_database(self, backup_path: str) -> bool:
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        try:
            import shutil
            shutil.copy2(self.db_path, backup_path)
            return True
        except Exception:
            return False

    def restore_database(self, backup_path: str) -> bool:
        """استعادة قاعدة البيانات من نسخة احتياطية"""
        try:
            import shutil
            if os.path.exists(backup_path):
                shutil.copy2(backup_path, self.db_path)
                return True
            return False
        except Exception:
            return False
