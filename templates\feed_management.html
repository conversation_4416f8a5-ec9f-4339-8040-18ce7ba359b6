{% extends "base.html" %}

{% block title %}إدارة الأعلاف - نظام إدارة مزرعة الأبقار{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h2">
        <i class="fas fa-seedling text-primary"></i>
        إدارة الأعلاف
    </h1>
    <a href="{{ url_for('add_feed_mix') }}" class="btn btn-primary">
        <i class="fas fa-plus"></i>
        إضافة خلطة جديدة
    </a>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-primary">{{ feed_mixes|length }}</h3>
                <p class="mb-0">إجمالي الخلطات</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-success">{{ feed_mixes|selectattr('is_active')|list|length }}</h3>
                <p class="mb-0">الخلطات النشطة</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-info">{{ "%.2f"|format((feed_mixes|selectattr('is_active')|map(attribute='cost_per_kg')|sum) / (feed_mixes|selectattr('is_active')|list|length) if feed_mixes|selectattr('is_active')|list|length > 0 else 0) }}</h3>
                <p class="mb-0">متوسط التكلفة (ريال/كغ)</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-warning">{{ "%.0f"|format((feed_mixes|selectattr('is_active')|map(attribute='quality_score')|sum) / (feed_mixes|selectattr('is_active')|list|length) if feed_mixes|selectattr('is_active')|list|length > 0 else 0) }}</h3>
                <p class="mb-0">متوسط الجودة (%)</p>
            </div>
        </div>
    </div>
</div>

<!-- جدول خلطات العلف -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list"></i>
            قائمة خلطات العلف
        </h5>
    </div>
    <div class="card-body">
        {% if feed_mixes %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>رقم الخلطة</th>
                        <th>اسم الخلطة</th>
                        <th>الوصف</th>
                        <th>التكلفة (ريال/كغ)</th>
                        <th>التكلفة (ريال/طن)</th>
                        <th>نقاط الجودة</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for mix in feed_mixes %}
                    <tr {% if not mix.is_active %}class="table-secondary"{% endif %}>
                        <td><strong>{{ mix.mix_id }}</strong></td>
                        <td>{{ mix.name }}</td>
                        <td>{{ mix.description[:50] }}{% if mix.description|length > 50 %}...{% endif %}</td>
                        <td>
                            <span class="badge bg-primary">{{ "%.3f"|format(mix.cost_per_kg) }}</span>
                        </td>
                        <td>
                            <span class="badge bg-info">{{ "%.2f"|format(mix.cost_per_ton) }}</span>
                        </td>
                        <td>
                            {% if mix.quality_score >= 80 %}
                                <span class="badge bg-success">{{ "%.0f"|format(mix.quality_score) }}%</span>
                            {% elif mix.quality_score >= 60 %}
                                <span class="badge bg-warning">{{ "%.0f"|format(mix.quality_score) }}%</span>
                            {% else %}
                                <span class="badge bg-danger">{{ "%.0f"|format(mix.quality_score) }}%</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if mix.is_active %}
                                <span class="badge bg-success">نشطة</span>
                            {% else %}
                                <span class="badge bg-secondary">معطلة</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('edit_feed_mix', mix_id=mix.mix_id) }}" 
                                   class="btn btn-outline-primary" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="btn btn-outline-info" 
                                        onclick="showMixDetails('{{ mix.mix_id }}')" title="التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button type="button" class="btn btn-outline-success" 
                                        onclick="analyzeMix('{{ mix.mix_id }}')" title="تحليل">
                                    <i class="fas fa-chart-line"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-seedling fa-5x text-muted mb-3"></i>
            <h4 class="text-muted">لا توجد خلطات علف</h4>
            <p class="text-muted">لم يتم إنشاء أي خلطات علف بعد</p>
            <a href="{{ url_for('add_feed_mix') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                إضافة خلطة جديدة
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- نافذة تفاصيل الخلطة -->
<div class="modal fade" id="mixDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل خلطة العلف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="mixDetailsContent">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- نافذة تحليل الخلطة -->
<div class="modal fade" id="mixAnalysisModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تحليل خلطة العلف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="mixAnalysisContent">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">جاري التحليل...</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// عرض تفاصيل الخلطة
function showMixDetails(mixId) {
    const modal = new bootstrap.Modal(document.getElementById('mixDetailsModal'));
    const content = document.getElementById('mixDetailsContent');

    content.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
        </div>
    `;

    modal.show();

    // استدعاء API لجلب تفاصيل الخلطة
    fetch(`/api/feed_mix/${mixId}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                content.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        خطأ: ${data.error}
                    </div>
                `;
                return;
            }

            content.innerHTML = generateMixDetailsHTML(data);
        })
        .catch(error => {
            content.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    خطأ في تحميل البيانات: ${error.message}
                </div>
            `;
        });
}

// تحليل الخلطة
function analyzeMix(mixId) {
    const modal = new bootstrap.Modal(document.getElementById('mixAnalysisModal'));
    const content = document.getElementById('mixAnalysisContent');

    content.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">جاري التحليل...</span>
            </div>
        </div>
    `;

    modal.show();

    // استدعاء API لتحليل الخلطة
    fetch(`/api/feed_mix/${mixId}/analysis`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                content.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        خطأ: ${data.error}
                    </div>
                `;
                return;
            }

            content.innerHTML = generateAnalysisHTML(data);
        })
        .catch(error => {
            content.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    خطأ في تحميل التحليل: ${error.message}
                </div>
            `;
        });
}

// توليد HTML لتفاصيل الخلطة
function generateMixDetailsHTML(data) {
    let html = `
        <div class="row">
            <div class="col-md-6">
                <h6 class="text-primary">
                    <i class="fas fa-info-circle"></i>
                    المعلومات الأساسية
                </h6>
                <table class="table table-sm">
                    <tr><td><strong>رقم الخلطة:</strong></td><td>${data.mix_id}</td></tr>
                    <tr><td><strong>اسم الخلطة:</strong></td><td>${data.name}</td></tr>
                    <tr><td><strong>الوصف:</strong></td><td>${data.description || 'غير محدد'}</td></tr>
                    <tr><td><strong>تاريخ الإنشاء:</strong></td><td>${data.creation_date}</td></tr>
                    <tr><td><strong>آخر تعديل:</strong></td><td>${data.last_modified}</td></tr>
                    <tr><td><strong>الحالة:</strong></td><td>
                        <span class="badge bg-${data.is_active ? 'success' : 'secondary'}">
                            ${data.is_active ? 'نشطة' : 'معطلة'}
                        </span>
                    </td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6 class="text-success">
                    <i class="fas fa-dollar-sign"></i>
                    التكلفة والجودة
                </h6>
                <table class="table table-sm">
                    <tr><td><strong>التكلفة (ريال/كغ):</strong></td><td>${data.cost_per_kg.toFixed(3)}</td></tr>
                    <tr><td><strong>التكلفة (ريال/طن):</strong></td><td>${data.cost_per_ton.toFixed(2)}</td></tr>
                    <tr><td><strong>نقاط الجودة:</strong></td><td>
                        <span class="badge bg-${data.quality_score >= 80 ? 'success' : data.quality_score >= 60 ? 'warning' : 'danger'}">
                            ${data.quality_score.toFixed(0)}%
                        </span>
                    </td></tr>
                </table>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <h6 class="text-info">
                    <i class="fas fa-seedling"></i>
                    مكونات الخلطة
                </h6>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>المكون</th>
                                <th>النسبة (%)</th>
                                <th>الفئة</th>
                                <th>البروتين (%)</th>
                                <th>الطاقة (ميجا كالوري/كغ)</th>
                            </tr>
                        </thead>
                        <tbody>
    `;

    if (data.ingredients_details && data.ingredients_details.length > 0) {
        data.ingredients_details.forEach(ingredient => {
            html += `
                <tr>
                    <td><strong>${ingredient.name_ar}</strong></td>
                    <td><span class="badge bg-primary">${ingredient.percentage.toFixed(1)}%</span></td>
                    <td>${ingredient.category || 'غير محدد'}</td>
                    <td>${ingredient.crude_protein ? ingredient.crude_protein.toFixed(1) : 'غير محدد'}</td>
                    <td>${ingredient.energy_mcal ? ingredient.energy_mcal.toFixed(2) : 'غير محدد'}</td>
                </tr>
            `;
        });
    } else {
        html += `
            <tr>
                <td colspan="5" class="text-center text-muted">لا توجد مكونات محددة</td>
            </tr>
        `;
    }

    html += `
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `;

    // إضافة التحليل الغذائي إذا كان متوفراً
    if (data.nutritional_analysis && Object.keys(data.nutritional_analysis).length > 0) {
        html += `
            <div class="row mt-4">
                <div class="col-12">
                    <h6 class="text-warning">
                        <i class="fas fa-chart-pie"></i>
                        التحليل الغذائي
                    </h6>
                    <div class="row">
        `;

        const nutrients = [
            {key: 'crude_protein', name: 'البروتين الخام', unit: '%', color: 'primary'},
            {key: 'energy_mcal', name: 'الطاقة', unit: 'ميجا كالوري/كغ', color: 'success'},
            {key: 'fiber', name: 'الألياف', unit: '%', color: 'info'},
            {key: 'fat', name: 'الدهون', unit: '%', color: 'warning'},
            {key: 'calcium', name: 'الكالسيوم', unit: '%', color: 'secondary'},
            {key: 'phosphorus', name: 'الفوسفور', unit: '%', color: 'dark'}
        ];

        nutrients.forEach(nutrient => {
            if (data.nutritional_analysis[nutrient.key]) {
                html += `
                    <div class="col-md-4 mb-3">
                        <div class="card border-${nutrient.color}">
                            <div class="card-body text-center">
                                <h6 class="card-title text-${nutrient.color}">${nutrient.name}</h6>
                                <h4 class="text-${nutrient.color}">
                                    ${data.nutritional_analysis[nutrient.key].toFixed(2)} ${nutrient.unit}
                                </h4>
                            </div>
                        </div>
                    </div>
                `;
            }
        });

        html += `
                    </div>
                </div>
            </div>
        `;
    }

    // إضافة التحذيرات والتوصيات
    if (data.warnings && data.warnings.length > 0) {
        html += `
            <div class="row mt-4">
                <div class="col-md-6">
                    <h6 class="text-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        تحذيرات
                    </h6>
                    <ul class="list-group">
        `;
        data.warnings.forEach(warning => {
            html += `<li class="list-group-item list-group-item-warning">${warning}</li>`;
        });
        html += `
                    </ul>
                </div>
            </div>
        `;
    }

    if (data.recommendations && data.recommendations.length > 0) {
        html += `
            <div class="row mt-4">
                <div class="col-md-6">
                    <h6 class="text-success">
                        <i class="fas fa-lightbulb"></i>
                        توصيات
                    </h6>
                    <ul class="list-group">
        `;
        data.recommendations.forEach(recommendation => {
            html += `<li class="list-group-item list-group-item-info">${recommendation}</li>`;
        });
        html += `
                    </ul>
                </div>
            </div>
        `;
    }

    return html;
}

// توليد HTML لتحليل الخلطة
function generateAnalysisHTML(data) {
    let html = `
        <div class="row">
            <div class="col-md-4">
                <div class="card border-primary">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-star"></i>
                            التقييم العام
                        </h6>
                    </div>
                    <div class="card-body text-center">
                        <h2 class="text-primary">${data.quality_score.toFixed(0)}%</h2>
                        <p class="mb-0">نقاط الجودة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-bar"></i>
                            التحليل الاقتصادي
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 text-center">
                                <h5 class="text-success">${data.economic_analysis.cost_per_kg.toFixed(3)}</h5>
                                <small class="text-muted">ريال/كغ</small>
                            </div>
                            <div class="col-md-4 text-center">
                                <h5 class="text-info">${data.economic_analysis.cost_per_ton.toFixed(2)}</h5>
                                <small class="text-muted">ريال/طن</small>
                            </div>
                            <div class="col-md-4 text-center">
                                <h5 class="text-warning">${data.economic_analysis.cost_effectiveness.toFixed(2)}</h5>
                                <small class="text-muted">فعالية التكلفة</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إضافة التحليل الغذائي المفصل
    if (data.nutritional_analysis) {
        html += `
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-microscope"></i>
                                التحليل الغذائي المفصل
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                التحليل الغذائي المفصل متاح في الإصدار المتقدم
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // إضافة التحذيرات والتوصيات
    if (data.warnings && data.warnings.length > 0) {
        html += `
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card border-warning">
                        <div class="card-header bg-warning text-dark">
                            <h6 class="mb-0">
                                <i class="fas fa-exclamation-triangle"></i>
                                تحذيرات
                            </h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled mb-0">
        `;
        data.warnings.forEach(warning => {
            html += `<li><i class="fas fa-exclamation-circle text-warning"></i> ${warning}</li>`;
        });
        html += `
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    if (data.recommendations && data.recommendations.length > 0) {
        html += `
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card border-success">
                        <div class="card-header bg-success text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-lightbulb"></i>
                                توصيات التحسين
                            </h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled mb-0">
        `;
        data.recommendations.forEach(recommendation => {
            html += `<li><i class="fas fa-check-circle text-success"></i> ${recommendation}</li>`;
        });
        html += `
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    return html;
}
</script>
{% endblock %}
