{% extends "base.html" %}

{% block title %}إدارة الأعلاف - نظام إدارة مزرعة الأبقار{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h2">
        <i class="fas fa-seedling text-primary"></i>
        إدارة الأعلاف
    </h1>
    <a href="{{ url_for('add_feed_mix') }}" class="btn btn-primary">
        <i class="fas fa-plus"></i>
        إضافة خلطة جديدة
    </a>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-primary">{{ feed_mixes|length }}</h3>
                <p class="mb-0">إجمالي الخلطات</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-success">{{ feed_mixes|selectattr('is_active')|list|length }}</h3>
                <p class="mb-0">الخلطات النشطة</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-info">{{ "%.2f"|format((feed_mixes|selectattr('is_active')|map(attribute='cost_per_kg')|sum) / (feed_mixes|selectattr('is_active')|list|length) if feed_mixes|selectattr('is_active')|list|length > 0 else 0) }}</h3>
                <p class="mb-0">متوسط التكلفة (ريال/كغ)</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-warning">{{ "%.0f"|format((feed_mixes|selectattr('is_active')|map(attribute='quality_score')|sum) / (feed_mixes|selectattr('is_active')|list|length) if feed_mixes|selectattr('is_active')|list|length > 0 else 0) }}</h3>
                <p class="mb-0">متوسط الجودة (%)</p>
            </div>
        </div>
    </div>
</div>

<!-- جدول خلطات العلف -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list"></i>
            قائمة خلطات العلف
        </h5>
    </div>
    <div class="card-body">
        {% if feed_mixes %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>رقم الخلطة</th>
                        <th>اسم الخلطة</th>
                        <th>الوصف</th>
                        <th>التكلفة (ريال/كغ)</th>
                        <th>التكلفة (ريال/طن)</th>
                        <th>نقاط الجودة</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for mix in feed_mixes %}
                    <tr {% if not mix.is_active %}class="table-secondary"{% endif %}>
                        <td><strong>{{ mix.mix_id }}</strong></td>
                        <td>{{ mix.name }}</td>
                        <td>{{ mix.description[:50] }}{% if mix.description|length > 50 %}...{% endif %}</td>
                        <td>
                            <span class="badge bg-primary">{{ "%.3f"|format(mix.cost_per_kg) }}</span>
                        </td>
                        <td>
                            <span class="badge bg-info">{{ "%.2f"|format(mix.cost_per_ton) }}</span>
                        </td>
                        <td>
                            {% if mix.quality_score >= 80 %}
                                <span class="badge bg-success">{{ "%.0f"|format(mix.quality_score) }}%</span>
                            {% elif mix.quality_score >= 60 %}
                                <span class="badge bg-warning">{{ "%.0f"|format(mix.quality_score) }}%</span>
                            {% else %}
                                <span class="badge bg-danger">{{ "%.0f"|format(mix.quality_score) }}%</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if mix.is_active %}
                                <span class="badge bg-success">نشطة</span>
                            {% else %}
                                <span class="badge bg-secondary">معطلة</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('edit_feed_mix', mix_id=mix.mix_id) }}" 
                                   class="btn btn-outline-primary" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="btn btn-outline-info" 
                                        onclick="showMixDetails('{{ mix.mix_id }}')" title="التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button type="button" class="btn btn-outline-success" 
                                        onclick="analyzeMix('{{ mix.mix_id }}')" title="تحليل">
                                    <i class="fas fa-chart-line"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-seedling fa-5x text-muted mb-3"></i>
            <h4 class="text-muted">لا توجد خلطات علف</h4>
            <p class="text-muted">لم يتم إنشاء أي خلطات علف بعد</p>
            <a href="{{ url_for('add_feed_mix') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                إضافة خلطة جديدة
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- نافذة تفاصيل الخلطة -->
<div class="modal fade" id="mixDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل خلطة العلف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="mixDetailsContent">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// عرض تفاصيل الخلطة
function showMixDetails(mixId) {
    const modal = new bootstrap.Modal(document.getElementById('mixDetailsModal'));
    const content = document.getElementById('mixDetailsContent');
    
    content.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
        </div>
    `;
    
    modal.show();
    
    // هنا يمكن إضافة استدعاء AJAX لجلب تفاصيل الخلطة
    setTimeout(() => {
        content.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                تفاصيل الخلطة ${mixId} ستظهر هنا
            </div>
        `;
    }, 1000);
}

// تحليل الخلطة
function analyzeMix(mixId) {
    alert(`تحليل الخلطة ${mixId} - هذه الميزة قيد التطوير`);
}
</script>
{% endblock %}
