# -*- coding: utf-8 -*-
"""
إعدادات النظام - System Configuration
ملف الإعدادات الرئيسي لنظام إدارة مزرعة الأبقار الحلوب
"""

import os
from datetime import timedelta

class Config:
    """إعدادات النظام الأساسية"""
    
    # إعدادات Flask
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dairy_farm_secret_key_2024_complex'
    
    # إعدادات قاعدة البيانات
    DATABASE_PATH = os.environ.get('DATABASE_PATH') or 'dairy_farm.db'
    SQLALCHEMY_DATABASE_URI = f'sqlite:///{DATABASE_PATH}'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # إعدادات الملفات
    UPLOAD_FOLDER = 'uploads'
    REPORTS_FOLDER = 'reports'
    CHARTS_FOLDER = 'static/charts'
    BACKUPS_FOLDER = 'backups'
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    
    # إعدادات الأمان
    SESSION_PERMANENT = False
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    
    # إعدادات التطبيق
    APP_NAME = 'نظام إدارة مزرعة الأبقار الحلوب'
    APP_VERSION = '1.0.0'
    APP_AUTHOR = 'فريق التطوير'
    
    # إعدادات اللغة والمنطقة
    DEFAULT_LANGUAGE = 'ar'
    DEFAULT_TIMEZONE = 'Asia/Riyadh'
    DATE_FORMAT = '%Y-%m-%d'
    DATETIME_FORMAT = '%Y-%m-%d %H:%M:%S'
    
    # إعدادات التقارير
    REPORTS_PER_PAGE = 50
    MAX_EXPORT_RECORDS = 10000
    
    # إعدادات النسخ الاحتياطي
    AUTO_BACKUP = True
    BACKUP_INTERVAL_DAYS = 7
    MAX_BACKUP_FILES = 30
    
    # إعدادات الإشعارات
    ENABLE_NOTIFICATIONS = True
    LOW_MILK_THRESHOLD = 5.0  # لتر
    HIGH_MILK_THRESHOLD = 50.0  # لتر
    
    # إعدادات الرسوم البيانية
    CHART_COLORS = [
        '#2E86AB',  # أزرق
        '#A23B72',  # بنفسجي
        '#F18F01',  # برتقالي
        '#C73E1D',  # أحمر
        '#28a745',  # أخضر
        '#6f42c1',  # بنفسجي فاتح
        '#fd7e14',  # برتقالي فاتح
        '#20c997'   # أخضر فاتح
    ]
    
    @staticmethod
    def init_app(app):
        """تهيئة التطبيق مع الإعدادات"""
        # إنشاء المجلدات المطلوبة
        folders = [
            Config.UPLOAD_FOLDER,
            Config.REPORTS_FOLDER,
            Config.CHARTS_FOLDER,
            Config.BACKUPS_FOLDER
        ]
        
        for folder in folders:
            os.makedirs(folder, exist_ok=True)

class DevelopmentConfig(Config):
    """إعدادات بيئة التطوير"""
    DEBUG = True
    TESTING = False
    
    # إعدادات قاعدة البيانات للتطوير
    DATABASE_PATH = 'dev_dairy_farm.db'
    SQLALCHEMY_DATABASE_URI = f'sqlite:///{DATABASE_PATH}'
    
    # إعدادات إضافية للتطوير
    SQLALCHEMY_ECHO = True  # عرض استعلامات SQL
    
class TestingConfig(Config):
    """إعدادات بيئة الاختبار"""
    TESTING = True
    DEBUG = True
    
    # قاعدة بيانات في الذاكرة للاختبارات
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    
    # تعطيل CSRF للاختبارات
    WTF_CSRF_ENABLED = False

class ProductionConfig(Config):
    """إعدادات بيئة الإنتاج"""
    DEBUG = False
    TESTING = False
    
    # إعدادات أمان إضافية للإنتاج
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    
    # إعدادات قاعدة البيانات للإنتاج
    # يمكن استخدام PostgreSQL أو MySQL
    DATABASE_URL = os.environ.get('DATABASE_URL')
    if DATABASE_URL:
        SQLALCHEMY_DATABASE_URI = DATABASE_URL
    
    # إعدادات التسجيل
    LOG_LEVEL = 'INFO'
    LOG_FILE = 'logs/dairy_farm.log'
    
    @classmethod
    def init_app(cls, app):
        Config.init_app(app)
        
        # إعداد التسجيل للإنتاج
        import logging
        from logging.handlers import RotatingFileHandler
        
        os.makedirs('logs', exist_ok=True)
        
        file_handler = RotatingFileHandler(
            cls.LOG_FILE,
            maxBytes=10240000,  # 10MB
            backupCount=10
        )
        
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)
        app.logger.setLevel(logging.INFO)
        app.logger.info('Dairy Farm Management System startup')

# إعدادات خاصة بالمزرعة
class FarmConfig:
    """إعدادات خاصة بالمزرعة"""
    
    # معلومات المزرعة
    FARM_NAME = 'مزرعة الأبقار النموذجية'
    FARM_OWNER = 'صاحب المزرعة'
    FARM_ADDRESS = 'العنوان'
    FARM_PHONE = '+966xxxxxxxxx'
    FARM_EMAIL = '<EMAIL>'
    
    # إعدادات القطيع
    DEFAULT_BREED = 'holstein'
    DEFAULT_WEIGHT = 600.0
    DEFAULT_FEED_INTAKE = 22.0
    DEFAULT_BODY_CONDITION = 3.0
    
    # إعدادات الحليب
    MILKING_TIMES = ['صباحي', 'مسائي']
    TARGET_DAILY_MILK = 25.0  # لتر
    MILK_PRICE_PER_LITER = 2.5  # ريال
    
    # إعدادات العلف
    DEFAULT_FEED_COST = 1.5  # ريال/كغ
    FEED_EFFICIENCY_TARGET = 1.5  # كغ علف/لتر حليب
    
    # إعدادات التنبيهات
    HEALTH_CHECK_INTERVAL = 30  # يوم
    VACCINATION_REMINDER = 90  # يوم
    DRY_PERIOD_LENGTH = 60  # يوم
    
    # إعدادات التقارير
    REPORT_LOGO = 'static/images/farm_logo.png'
    REPORT_FOOTER = f'تم إنشاؤه بواسطة {Config.APP_NAME}'

# خريطة الإعدادات
config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}

def get_config():
    """الحصول على إعدادات البيئة الحالية"""
    env = os.environ.get('FLASK_ENV', 'development')
    return config.get(env, config['default'])
