{% extends "base.html" %}

{% block title %}تعديل البقرة {{ cow.name }} - نظام إدارة مزرعة الأبقار{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h2">
        <i class="fas fa-edit text-primary"></i>
        تعديل البقرة: {{ cow.name }}
    </h1>
    <a href="{{ url_for('herd_management') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-right"></i>
        العودة للقائمة
    </a>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <form method="POST" class="needs-validation" novalidate>
            <!-- المعلومات الأساسية -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle"></i>
                        المعلومات الأساسية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="cow_id" class="form-label">رقم البقرة</label>
                            <input type="text" class="form-control" id="cow_id" name="cow_id" 
                                   value="{{ cow.cow_id }}" readonly>
                            <div class="form-text">لا يمكن تغيير رقم البقرة</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">اسم البقرة</label>
                            <input type="text" class="form-control" id="name" name="name"
                                   value="{{ cow.name }}" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="breed" class="form-label">السلالة</label>
                            <select class="form-select" id="breed" name="breed" required>
                                {% for breed_key, breed_info in breed_weights.items() %}
                                <option value="{{ breed_key }}" {% if cow.breed == breed_key %}selected{% endif %}>
                                    {{ breed_info.name_ar }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="birth_date" class="form-label">تاريخ الميلاد</label>
                            <input type="date" class="form-control" id="birth_date" name="birth_date" 
                                   value="{{ cow.birth_date.strftime('%Y-%m-%d') }}" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="weight" class="form-label">الوزن (كغ)</label>
                            <input type="number" class="form-control" id="weight" name="weight" 
                                   min="200" max="1000" step="0.1" value="{{ cow.weight }}" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="form-check mt-4">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                       {% if cow.is_active %}checked{% endif %}>
                                <label class="form-check-label" for="is_active">
                                    بقرة نشطة
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات الإنتاج -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-tint"></i>
                        معلومات الإنتاج
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="last_calving_date" class="form-label">تاريخ آخر ولادة</label>
                            <input type="date" class="form-control" id="last_calving_date" name="last_calving_date"
                                   value="{% if cow.last_calving_date %}{{ cow.last_calving_date.strftime('%Y-%m-%d') }}{% endif %}">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="lactation_number" class="form-label">رقم الموسم</label>
                            <select class="form-select" id="lactation_number" name="lactation_number">
                                {% for i in range(1, 11) %}
                                <option value="{{ i }}" {% if cow.lactation_number == i %}selected{% endif %}>
                                    الموسم {{ i }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="health_status" class="form-label">الحالة الصحية</label>
                            <select class="form-select" id="health_status" name="health_status">
                                <option value="سليمة" {% if cow.health_status == 'سليمة' %}selected{% endif %}>سليمة</option>
                                <option value="مريضة" {% if cow.health_status == 'مريضة' %}selected{% endif %}>مريضة</option>
                                <option value="حامل" {% if cow.health_status == 'حامل' %}selected{% endif %}>حامل</option>
                                <option value="جافة" {% if cow.health_status == 'جافة' %}selected{% endif %}>جافة</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="daily_feed_intake" class="form-label">استهلاك العلف اليومي (كغ)</label>
                            <input type="number" class="form-control" id="daily_feed_intake" name="daily_feed_intake" 
                                   min="10" max="40" step="0.1" value="{{ cow.daily_feed_intake }}">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="body_condition_score" class="form-label">حالة الجسم (1-5)</label>
                            <input type="number" class="form-control" id="body_condition_score" name="body_condition_score" 
                                   min="1" max="5" step="0.1" value="{{ cow.body_condition_score }}">
                        </div>
                    </div>
                </div>
            </div>

            <!-- ملاحظات -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-sticky-note"></i>
                        ملاحظات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="4">{{ cow.notes }}</textarea>
                    </div>
                </div>
            </div>

            <!-- أزرار الحفظ -->
            <div class="card">
                <div class="card-body">
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('herd_management') }}" class="btn btn-outline-secondary me-md-2">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ التغييرات
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- معلومات الإنتاج الحالية -->
<div class="row justify-content-center mt-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-line"></i>
                    معلومات الإنتاج الحالية
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <h5 class="text-primary">{{ "%.1f"|format(cow.age_in_years) }}</h5>
                        <small class="text-muted">العمر (سنة)</small>
                    </div>
                    <div class="col-md-3">
                        <h5 class="text-success">{{ cow.days_in_milk }}</h5>
                        <small class="text-muted">أيام الحليب</small>
                    </div>
                    <div class="col-md-3">
                        <h5 class="text-info">{{ "%.1f"|format(cow.average_daily_milk) }}</h5>
                        <small class="text-muted">متوسط الإنتاج (لتر)</small>
                    </div>
                    <div class="col-md-3">
                        <h5 class="text-warning">{{ "%.1f"|format(cow.total_lactation_milk) }}</h5>
                        <small class="text-muted">إجمالي الموسم (لتر)</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// التحقق من صحة النموذج
(function() {
    'use strict';
    window.addEventListener('load', function() {
        const forms = document.getElementsByClassName('needs-validation');
        const validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
{% endblock %}
